# 🤖 Configuration du Support AI avec Gemini 2.0 Flash

## 📋 Prérequis

1. **Clé API Gemini** : <PERSON><PERSON> devez obtenir une clé API de Google AI Studio
2. **Python 3.8+** avec les dépendances installées
3. **Accès administrateur** au système POS

## 🔑 Étape 1 : Obtenir la clé API Gemini

1. Visitez [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Connectez-vous avec votre compte Google
3. Cliquez sur "Create API Key"
4. Copiez la clé générée (format : `AIza...`)

## ⚙️ Étape 2 : Configuration

### C<PERSON>er le fichier .env

Créez un fichier `.env` à la racine du projet avec le contenu suivant :

```bash
# Configuration de base (si pas déjà présent)
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///app.db
REDIS_URL=redis://localhost:6379/0

# Configuration Gemini AI - OBLIGATOIRE
GEMINI_API_KEY=votre-cle-api-ici
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=2048
GEMINI_TOP_P=0.8
GEMINI_TOP_K=40

# Configuration du support AI
AI_SUPPORT_ENABLED=True
AI_CONFIDENCE_THRESHOLD=0.7
AI_MAX_CONVERSATION_LENGTH=20
AI_RESPONSE_TIMEOUT=30
AI_AUTO_ESCALATION=True
AI_RETRY_ATTEMPTS=3
AI_RETRY_DELAY=1.0
```

### Installer les dépendances

```bash
pip install -r requirements.txt
```

## 🚀 Étape 3 : Démarrage et test

1. **Démarrer l'application** :
   ```bash
   python run.py
   ```

2. **Accéder au support AI** :
   - URL : `http://localhost:5000/support`
   - Ou via le menu "Support AI" dans la navigation

3. **Tester la connexion Gemini** :
   - Allez dans `http://localhost:5000/support/admin` (admin requis)
   - Cliquez sur "Tester" à côté de "Service Gemini AI"
   - Vous devriez voir "✅ Connexion réussie !"

## 📚 Étape 4 : Initialiser la base de connaissances

1. **Accéder au tableau de bord admin** :
   - URL : `http://localhost:5000/support/admin`
   - Connectez-vous avec un compte administrateur

2. **Initialiser les données d'exemple** :
   - Cliquez sur "Initialiser données d'exemple"
   - Cela créera 5 articles de base dans la base de connaissances

3. **Vérifier la base de connaissances** :
   - Allez dans `http://localhost:5000/support/knowledge-base`
   - Vous devriez voir les articles créés

## 🧪 Étape 5 : Tester le chat AI

1. **Accéder au chat** :
   - URL : `http://localhost:5000/support/chat`
   - Ou cliquez sur "Chat en direct" dans le menu

2. **Tester des questions** :
   ```
   Comment utiliser la caisse ?
   J'ai un problème avec l'inventaire
   Comment générer un rapport ?
   L'imprimante ne fonctionne pas
   ```

3. **Vérifier les réponses** :
   - L'IA devrait répondre en français
   - Les réponses doivent être contextualisées au système POS
   - Un score de confiance doit être affiché

## 🎫 Étape 6 : Tester les tickets

1. **Créer un ticket** :
   - URL : `http://localhost:5000/support/tickets/create`
   - Remplissez le formulaire avec un problème

2. **Vérifier l'escalade automatique** :
   - Si l'IA ne peut pas résoudre le problème
   - Un ticket sera automatiquement escaladé vers un agent humain

## 🔧 Dépannage

### Problème : "Service non configuré"

**Cause** : La clé API Gemini n'est pas configurée ou invalide

**Solution** :
1. Vérifiez que `GEMINI_API_KEY` est dans votre fichier `.env`
2. Vérifiez que la clé commence par `AIza`
3. Testez la clé sur [Google AI Studio](https://makersuite.google.com/)

### Problème : "Erreur de connexion"

**Cause** : Problème réseau ou quota API dépassé

**Solution** :
1. Vérifiez votre connexion internet
2. Vérifiez les quotas sur [Google Cloud Console](https://console.cloud.google.com/)
3. Attendez quelques minutes et réessayez

### Problème : "Template not found"

**Cause** : Templates manquants

**Solution** :
1. Vérifiez que tous les fichiers dans `app/modules/ai_support/templates/` existent
2. Redémarrez l'application

### Problème : L'IA ne répond pas

**Cause** : Service non initialisé ou erreur de configuration

**Solution** :
1. Allez dans le tableau de bord admin
2. Cliquez sur "Tester" pour vérifier la connexion
3. Vérifiez les logs de l'application pour les erreurs

## 📊 Monitoring et maintenance

### Vérifications régulières

1. **Quotas API** : Surveillez votre utilisation sur Google Cloud Console
2. **Performance** : Vérifiez les temps de réponse dans le tableau de bord
3. **Satisfaction** : Consultez les évaluations des utilisateurs

### Optimisation

1. **Ajustez la température** : Plus bas = plus précis, plus haut = plus créatif
2. **Modifiez le seuil de confiance** : Plus bas = moins d'escalades
3. **Enrichissez la base de connaissances** : Ajoutez plus d'articles pertinents

## 🆘 Support

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** de l'application Flask
2. **Testez la connexion** dans le tableau de bord admin
3. **Consultez la documentation** Gemini AI
4. **Créez un ticket** dans le système lui-même !

## 🔒 Sécurité

- ⚠️ **Ne jamais** commiter votre clé API dans le code
- 🔐 Utilisez des variables d'environnement
- 🛡️ Limitez les quotas API pour éviter les abus
- 📝 Surveillez les logs pour détecter les utilisations anormales

---

**✅ Une fois configuré, votre système POS disposera d'un support client automatisé 24h/24 et 7j/7 !**
