from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    def day_of_week(self) -> str:
        day = self.date("%w")
        DAY_NAMES = {
            "0": "hétfő",
            "1": "kedd",
            "2": "szerda",
            "3": "cs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            "4": "péntek",
            "5": "szombat",
            "6": "vas<PERSON>rna<PERSON>",
        }

        return DAY_NAMES[day]

    def month_name(self) -> str:
        month = self.month()
        MONTH_NAMES = {
            "01": "január",
            "02": "febru<PERSON>r",
            "03": "március",
            "04": "április",
            "05": "május",
            "06": "junius",
            "07": "julius",
            "08": "augusztus",
            "09": "szeptember",
            "10": "október",
            "11": "november",
            "12": "december",
        }

        return MONTH_NAMES[month]
