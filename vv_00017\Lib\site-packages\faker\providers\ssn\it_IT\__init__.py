"""it_IT ssn provider (yields italian fiscal codes)"""

import unicodedata

from string import ascii_uppercase, digits

from .. import Provider as SsnProvider

ALPHABET = ascii_uppercase
ALPHANUMERICS = sorted(digits + ascii_uppercase)
ALPHANUMERICS_DICT = {char: index for index, char in enumerate(ALPHANUMERICS)}
MONTHS_LIST = ("A", "B", "C", "D", "E", "H", "L", "M", "P", "R", "S", "T")
VOWELS = "AEIOU"
CONSONANTS = [letter for letter in ALPHABET if letter not in VOWELS]


MUNICIPALITIES_LIST = (
    "A001",
    "A004",
    "A005",
    "A006",
    "A007",
    "A008",
    "A010",
    "A013",
    "A014",
    "A015",
    "A016",
    "A017",
    "A018",
    "A019",
    "A020",
    "A022",
    "A023",
    "A024",
    "A025",
    "A026",
    "A027",
    "A028",
    "A029",
    "A032",
    "A033",
    "A034",
    "A035",
    "A038",
    "A039",
    "A040",
    "A041",
    "A043",
    "A044",
    "A045",
    "A047",
    "A048",
    "A049",
    "A050",
    "A051",
    "A052",
    "A053",
    "A054",
    "A055",
    "A056",
    "A057",
    "A058",
    "A059",
    "A060",
    "A061",
    "A062",
    "A064",
    "A065",
    "A067",
    "A068",
    "A069",
    "A070",
    "A071",
    "A072",
    "A074",
    "A075",
    "A076",
    "A077",
    "A080",
    "A081",
    "A082",
    "A083",
    "A084",
    "A085",
    "A087",
    "A088",
    "A089",
    "A091",
    "A092",
    "A093",
    "A094",
    "A096",
    "A097",
    "A098",
    "A100",
    "A101",
    "A102",
    "A103",
    "A105",
    "A106",
    "A107",
    "A108",
    "A109",
    "A110",
    "A111",
    "A112",
    "A113",
    "A115",
    "A116",
    "A117",
    "A118",
    "A119",
    "A120",
    "A121",
    "A122",
    "A123",
    "A124",
    "A125",
    "A126",
    "A127",
    "A128",
    "A129",
    "A130",
    "A131",
    "A132",
    "A134",
    "A135",
    "A137",
    "A138",
    "A139",
    "A143",
    "A145",
    "A146",
    "A149",
    "A150",
    "A153",
    "A154",
    "A155",
    "A157",
    "A158",
    "A159",
    "A160",
    "A161",
    "A162",
    "A163",
    "A164",
    "A165",
    "A166",
    "A167",
    "A171",
    "A172",
    "A173",
    "A175",
    "A176",
    "A177",
    "A178",
    "A179",
    "A180",
    "A181",
    "A182",
    "A183",
    "A184",
    "A185",
    "A186",
    "A187",
    "A188",
    "A189",
    "A191",
    "A192",
    "A193",
    "A194",
    "A195",
    "A196",
    "A197",
    "A198",
    "A200",
    "A201",
    "A202",
    "A203",
    "A204",
    "A205",
    "A206",
    "A207",
    "A208",
    "A210",
    "A214",
    "A215",
    "A216",
    "A217",
    "A218",
    "A220",
    "A221",
    "A222",
    "A223",
    "A224",
    "A225",
    "A226",
    "A227",
    "A228",
    "A229",
    "A230",
    "A231",
    "A233",
    "A234",
    "A235",
    "A236",
    "A237",
    "A238",
    "A239",
    "A240",
    "A241",
    "A242",
    "A243",
    "A244",
    "A245",
    "A246",
    "A249",
    "A251",
    "A252",
    "A253",
    "A254",
    "A255",
    "A256",
    "A257",
    "A258",
    "A259",
    "A261",
    "A262",
    "A263",
    "A264",
    "A265",
    "A266",
    "A267",
    "A268",
    "A269",
    "A270",
    "A271",
    "A272",
    "A273",
    "A274",
    "A275",
    "A278",
    "A280",
    "A281",
    "A282",
    "A283",
    "A284",
    "A285",
    "A286",
    "A287",
    "A288",
    "A290",
    "A291",
    "A292",
    "A293",
    "A294",
    "A295",
    "A296",
    "A297",
    "A299",
    "A300",
    "A301",
    "A302",
    "A303",
    "A304",
    "A305",
    "A306",
    "A308",
    "A309",
    "A310",
    "A312",
    "A313",
    "A314",
    "A315",
    "A317",
    "A318",
    "A319",
    "A320",
    "A321",
    "A323",
    "A324",
    "A325",
    "A326",
    "A327",
    "A328",
    "A329",
    "A330",
    "A332",
    "A333",
    "A334",
    "A335",
    "A337",
    "A338",
    "A339",
    "A340",
    "A341",
    "A343",
    "A344",
    "A345",
    "A346",
    "A347",
    "A348",
    "A350",
    "A351",
    "A352",
    "A354",
    "A355",
    "A357",
    "A358",
    "A359",
    "A360",
    "A363",
    "A365",
    "A366",
    "A367",
    "A368",
    "A369",
    "A370",
    "A371",
    "A372",
    "A373",
    "A374",
    "A375",
    "A376",
    "A377",
    "A379",
    "A380",
    "A382",
    "A383",
    "A385",
    "A386",
    "A387",
    "A388",
    "A389",
    "A390",
    "A391",
    "A392",
    "A393",
    "A394",
    "A396",
    "A397",
    "A398",
    "A399",
    "A400",
    "A401",
    "A402",
    "A403",
    "A405",
    "A407",
    "A409",
    "A412",
    "A413",
    "A414",
    "A415",
    "A418",
    "A419",
    "A421",
    "A422",
    "A424",
    "A425",
    "A427",
    "A429",
    "A430",
    "A431",
    "A432",
    "A433",
    "A434",
    "A435",
    "A436",
    "A437",
    "A438",
    "A439",
    "A440",
    "A441",
    "A443",
    "A444",
    "A445",
    "A446",
    "A447",
    "A448",
    "A449",
    "A451",
    "A452",
    "A453",
    "A454",
    "A455",
    "A458",
    "A459",
    "A460",
    "A461",
    "A462",
    "A463",
    "A464",
    "A465",
    "A466",
    "A467",
    "A468",
    "A470",
    "A471",
    "A472",
    "A473",
    "A474",
    "A475",
    "A476",
    "A477",
    "A478",
    "A479",
    "A480",
    "A481",
    "A482",
    "A484",
    "A485",
    "A486",
    "A487",
    "A488",
    "A489",
    "A490",
    "A491",
    "A492",
    "A494",
    "A495",
    "A496",
    "A497",
    "A499",
    "A501",
    "A502",
    "A503",
    "A506",
    "A507",
    "A508",
    "A509",
    "A511",
    "A512",
    "A514",
    "A515",
    "A516",
    "A517",
    "A518",
    "A519",
    "A520",
    "A521",
    "A522",
    "A523",
    "A525",
    "A526",
    "A527",
    "A528",
    "A529",
    "A530",
    "A531",
    "A532",
    "A533",
    "A534",
    "A535",
    "A536",
    "A537",
    "A538",
    "A539",
    "A540",
    "A541",
    "A542",
    "A544",
    "A546",
    "A547",
    "A550",
    "A551",
    "A552",
    "A553",
    "A555",
    "A557",
    "A560",
    "A561",
    "A562",
    "A564",
    "A565",
    "A566",
    "A567",
    "A568",
    "A569",
    "A570",
    "A571",
    "A572",
    "A573",
    "A574",
    "A575",
    "A576",
    "A577",
    "A578",
    "A579",
    "A580",
    "A581",
    "A584",
    "A586",
    "A587",
    "A588",
    "A589",
    "A590",
    "A591",
    "A592",
    "A593",
    "A594",
    "A597",
    "A599",
    "A600",
    "A601",
    "A603",
    "A604",
    "A605",
    "A606",
    "A607",
    "A609",
    "A610",
    "A612",
    "A613",
    "A614",
    "A615",
    "A616",
    "A617",
    "A618",
    "A619",
    "A621",
    "A625",
    "A626",
    "A628",
    "A629",
    "A630",
    "A631",
    "A632",
    "A634",
    "A635",
    "A637",
    "A638",
    "A640",
    "A643",
    "A645",
    "A646",
    "A647",
    "A650",
    "A651",
    "A652",
    "A653",
    "A655",
    "A656",
    "A657",
    "A658",
    "A660",
    "A661",
    "A662",
    "A663",
    "A664",
    "A665",
    "A666",
    "A667",
    "A668",
    "A669",
    "A670",
    "A671",
    "A673",
    "A674",
    "A676",
    "A677",
    "A678",
    "A681",
    "A683",
    "A684",
    "A686",
    "A687",
    "A689",
    "A690",
    "A691",
    "A692",
    "A694",
    "A696",
    "A697",
    "A698",
    "A699",
    "A700",
    "A701",
    "A702",
    "A703",
    "A704",
    "A705",
    "A706",
    "A707",
    "A708",
    "A709",
    "A710",
    "A712",
    "A713",
    "A714",
    "A716",
    "A717",
    "A718",
    "A719",
    "A720",
    "A721",
    "A722",
    "A725",
    "A728",
    "A729",
    "A730",
    "A731",
    "A732",
    "A733",
    "A734",
    "A735",
    "A736",
    "A737",
    "A738",
    "A739",
    "A740",
    "A741",
    "A742",
    "A743",
    "A745",
    "A746",
    "A747",
    "A749",
    "A750",
    "A751",
    "A752",
    "A755",
    "A756",
    "A757",
    "A759",
    "A760",
    "A761",
    "A762",
    "A763",
    "A764",
    "A765",
    "A766",
    "A768",
    "A769",
    "A770",
    "A771",
    "A772",
    "A773",
    "A774",
    "A776",
    "A777",
    "A778",
    "A779",
    "A780",
    "A781",
    "A782",
    "A783",
    "A784",
    "A785",
    "A786",
    "A787",
    "A788",
    "A789",
    "A791",
    "A792",
    "A793",
    "A794",
    "A795",
    "A796",
    "A798",
    "A799",
    "A801",
    "A802",
    "A804",
    "A805",
    "A809",
    "A810",
    "A811",
    "A812",
    "A813",
    "A815",
    "A816",
    "A817",
    "A818",
    "A819",
    "A820",
    "A821",
    "A823",
    "A825",
    "A826",
    "A827",
    "A831",
    "A832",
    "A834",
    "A835",
    "A836",
    "A837",
    "A841",
    "A842",
    "A843",
    "A844",
    "A845",
    "A846",
    "A847",
    "A848",
    "A849",
    "A850",
    "A851",
    "A852",
    "A853",
    "A854",
    "A855",
    "A856",
    "A857",
    "A859",
    "A861",
    "A863",
    "A864",
    "A870",
    "A872",
    "A874",
    "A876",
    "A877",
    "A878",
    "A880",
    "A881",
    "A882",
    "A883",
    "A884",
    "A885",
    "A887",
    "A889",
    "A891",
    "A892",
    "A893",
    "A894",
    "A895",
    "A896",
    "A897",
    "A898",
    "A902",
    "A903",
    "A904",
    "A905",
    "A906",
    "A909",
    "A910",
    "A911",
    "A912",
    "A914",
    "A916",
    "A918",
    "A919",
    "A920",
    "A922",
    "A925",
    "A929",
    "A930",
    "A931",
    "A932",
    "A937",
    "A940",
    "A941",
    "A942",
    "A944",
    "A945",
    "A946",
    "A947",
    "A948",
    "A949",
    "A950",
    "A952",
    "A953",
    "A954",
    "A955",
    "A956",
    "A957",
    "A958",
    "A959",
    "A960",
    "A961",
    "A962",
    "A963",
    "A964",
    "A965",
    "A968",
    "A970",
    "A971",
    "A972",
    "A973",
    "A975",
    "A976",
    "A977",
    "A978",
    "A979",
    "A981",
    "A982",
    "A983",
    "A984",
    "A986",
    "A987",
    "A988",
    "A989",
    "A990",
    "A991",
    "A992",
    "A993",
    "A995",
    "A996",
    "A998",
    "A999",
    "B001",
    "B002",
    "B003",
    "B005",
    "B006",
    "B007",
    "B008",
    "B009",
    "B010",
    "B012",
    "B014",
    "B015",
    "B016",
    "B017",
    "B018",
    "B019",
    "B020",
    "B021",
    "B024",
    "B025",
    "B026",
    "B028",
    "B029",
    "B030",
    "B031",
    "B033",
    "B034",
    "B035",
    "B036",
    "B037",
    "B038",
    "B040",
    "B041",
    "B042",
    "B043",
    "B044",
    "B046",
    "B048",
    "B049",
    "B051",
    "B054",
    "B055",
    "B056",
    "B057",
    "B058",
    "B061",
    "B062",
    "B063",
    "B064",
    "B067",
    "B068",
    "B069",
    "B070",
    "B071",
    "B073",
    "B075",
    "B076",
    "B077",
    "B079",
    "B080",
    "B081",
    "B082",
    "B083",
    "B084",
    "B085",
    "B086",
    "B088",
    "B091",
    "B094",
    "B097",
    "B098",
    "B099",
    "B100",
    "B101",
    "B102",
    "B104",
    "B105",
    "B106",
    "B107",
    "B109",
    "B110",
    "B111",
    "B112",
    "B114",
    "B115",
    "B116",
    "B117",
    "B118",
    "B120",
    "B121",
    "B123",
    "B124",
    "B126",
    "B128",
    "B129",
    "B131",
    "B132",
    "B134",
    "B137",
    "B138",
    "B141",
    "B142",
    "B143",
    "B144",
    "B145",
    "B149",
    "B150",
    "B152",
    "B153",
    "B154",
    "B156",
    "B157",
    "B158",
    "B159",
    "B160",
    "B161",
    "B162",
    "B166",
    "B167",
    "B169",
    "B171",
    "B172",
    "B173",
    "B175",
    "B176",
    "B178",
    "B179",
    "B180",
    "B181",
    "B182",
    "B183",
    "B184",
    "B187",
    "B188",
    "B191",
    "B192",
    "B193",
    "B194",
    "B195",
    "B196",
    "B197",
    "B198",
    "B200",
    "B201",
    "B202",
    "B203",
    "B204",
    "B205",
    "B207",
    "B209",
    "B212",
    "B213",
    "B214",
    "B215",
    "B216",
    "B217",
    "B218",
    "B219",
    "B220",
    "B221",
    "B223",
    "B225",
    "B227",
    "B228",
    "B229",
    "B230",
    "B232",
    "B234",
    "B235",
    "B236",
    "B237",
    "B238",
    "B239",
    "B240",
    "B242",
    "B243",
    "B246",
    "B247",
    "B248",
    "B249",
    "B250",
    "B251",
    "B255",
    "B256",
    "B258",
    "B259",
    "B261",
    "B262",
    "B264",
    "B265",
    "B266",
    "B267",
    "B268",
    "B269",
    "B270",
    "B272",
    "B274",
    "B275",
    "B276",
    "B278",
    "B279",
    "B280",
    "B281",
    "B282",
    "B284",
    "B285",
    "B286",
    "B287",
    "B288",
    "B289",
    "B292",
    "B293",
    "B294",
    "B295",
    "B296",
    "B297",
    "B300",
    "B301",
    "B302",
    "B303",
    "B304",
    "B305",
    "B306",
    "B309",
    "B310",
    "B311",
    "B312",
    "B313",
    "B314",
    "B315",
    "B317",
    "B319",
    "B326",
    "B328",
    "B332",
    "B335",
    "B345",
    "B346",
    "B349",
    "B350",
    "B351",
    "B352",
    "B354",
    "B355",
    "B357",
    "B358",
    "B361",
    "B362",
    "B364",
    "B365",
    "B366",
    "B367",
    "B368",
    "B369",
    "B371",
    "B374",
    "B375",
    "B376",
    "B377",
    "B378",
    "B379",
    "B380",
    "B381",
    "B382",
    "B383",
    "B384",
    "B385",
    "B387",
    "B388",
    "B389",
    "B390",
    "B391",
    "B392",
    "B393",
    "B394",
    "B395",
    "B396",
    "B397",
    "B398",
    "B399",
    "B400",
    "B402",
    "B403",
    "B404",
    "B405",
    "B406",
    "B408",
    "B409",
    "B410",
    "B413",
    "B415",
    "B416",
    "B417",
    "B418",
    "B419",
    "B423",
    "B424",
    "B425",
    "B426",
    "B427",
    "B428",
    "B429",
    "B430",
    "B431",
    "B432",
    "B433",
    "B434",
    "B435",
    "B436",
    "B437",
    "B439",
    "B440",
    "B441",
    "B442",
    "B443",
    "B444",
    "B445",
    "B446",
    "B447",
    "B448",
    "B450",
    "B452",
    "B453",
    "B455",
    "B457",
    "B460",
    "B461",
    "B462",
    "B463",
    "B465",
    "B466",
    "B467",
    "B468",
    "B469",
    "B470",
    "B471",
    "B472",
    "B473",
    "B474",
    "B476",
    "B477",
    "B481",
    "B482",
    "B483",
    "B484",
    "B485",
    "B486",
    "B490",
    "B491",
    "B492",
    "B493",
    "B494",
    "B496",
    "B497",
    "B498",
    "B499",
    "B500",
    "B501",
    "B502",
    "B504",
    "B505",
    "B506",
    "B507",
    "B509",
    "B510",
    "B511",
    "B512",
    "B513",
    "B514",
    "B515",
    "B516",
    "B519",
    "B520",
    "B521",
    "B522",
    "B524",
    "B525",
    "B526",
    "B527",
    "B528",
    "B529",
    "B530",
    "B531",
    "B532",
    "B533",
    "B534",
    "B535",
    "B536",
    "B537",
    "B538",
    "B539",
    "B540",
    "B541",
    "B542",
    "B543",
    "B544",
    "B546",
    "B549",
    "B550",
    "B551",
    "B553",
    "B554",
    "B555",
    "B556",
    "B557",
    "B559",
    "B561",
    "B562",
    "B563",
    "B564",
    "B565",
    "B566",
    "B567",
    "B569",
    "B570",
    "B572",
    "B573",
    "B574",
    "B576",
    "B577",
    "B578",
    "B579",
    "B580",
    "B581",
    "B582",
    "B584",
    "B586",
    "B587",
    "B588",
    "B589",
    "B590",
    "B591",
    "B592",
    "B593",
    "B594",
    "B595",
    "B597",
    "B598",
    "B602",
    "B603",
    "B604",
    "B605",
    "B606",
    "B607",
    "B608",
    "B609",
    "B610",
    "B612",
    "B613",
    "B615",
    "B616",
    "B617",
    "B618",
    "B619",
    "B620",
    "B621",
    "B624",
    "B626",
    "B627",
    "B628",
    "B629",
    "B630",
    "B631",
    "B632",
    "B633",
    "B634",
    "B635",
    "B636",
    "B637",
    "B639",
    "B640",
    "B641",
    "B642",
    "B643",
    "B644",
    "B645",
    "B646",
    "B647",
    "B648",
    "B649",
    "B650",
    "B651",
    "B653",
    "B655",
    "B656",
    "B658",
    "B660",
    "B661",
    "B662",
    "B663",
    "B664",
    "B666",
    "B667",
    "B669",
    "B670",
    "B671",
    "B672",
    "B674",
    "B675",
    "B676",
    "B677",
    "B678",
    "B679",
    "B680",
    "B681",
    "B682",
    "B684",
    "B685",
    "B686",
    "B687",
    "B688",
    "B689",
    "B690",
    "B691",
    "B692",
    "B693",
    "B694",
    "B695",
    "B696",
    "B697",
    "B698",
    "B701",
    "B703",
    "B704",
    "B705",
    "B706",
    "B707",
    "B708",
    "B709",
    "B710",
    "B711",
    "B712",
    "B715",
    "B716",
    "B717",
    "B718",
    "B719",
    "B720",
    "B722",
    "B724",
    "B725",
    "B726",
    "B727",
    "B729",
    "B730",
    "B731",
    "B732",
    "B733",
    "B734",
    "B735",
    "B736",
    "B738",
    "B740",
    "B741",
    "B742",
    "B743",
    "B744",
    "B745",
    "B748",
    "B749",
    "B752",
    "B754",
    "B755",
    "B756",
    "B758",
    "B759",
    "B760",
    "B762",
    "B763",
    "B765",
    "B766",
    "B767",
    "B768",
    "B769",
    "B771",
    "B772",
    "B774",
    "B776",
    "B777",
    "B778",
    "B779",
    "B780",
    "B781",
    "B782",
    "B783",
    "B784",
    "B785",
    "B787",
    "B788",
    "B789",
    "B790",
    "B791",
    "B792",
    "B794",
    "B795",
    "B796",
    "B798",
    "B799",
    "B801",
    "B802",
    "B803",
    "B804",
    "B805",
    "B807",
    "B808",
    "B809",
    "B810",
    "B812",
    "B813",
    "B816",
    "B817",
    "B818",
    "B819",
    "B820",
    "B822",
    "B823",
    "B824",
    "B825",
    "B826",
    "B827",
    "B828",
    "B829",
    "B830",
    "B832",
    "B835",
    "B836",
    "B838",
    "B839",
    "B840",
    "B841",
    "B842",
    "B844",
    "B845",
    "B846",
    "B847",
    "B848",
    "B850",
    "B851",
    "B853",
    "B854",
    "B856",
    "B857",
    "B858",
    "B859",
    "B860",
    "B861",
    "B862",
    "B864",
    "B865",
    "B866",
    "B867",
    "B868",
    "B869",
    "B870",
    "B871",
    "B872",
    "B873",
    "B875",
    "B876",
    "B877",
    "B878",
    "B879",
    "B880",
    "B881",
    "B882",
    "B883",
    "B885",
    "B886",
    "B887",
    "B888",
    "B889",
    "B890",
    "B891",
    "B892",
    "B893",
    "B894",
    "B895",
    "B896",
    "B897",
    "B898",
    "B899",
    "B900",
    "B901",
    "B902",
    "B903",
    "B904",
    "B905",
    "B906",
    "B907",
    "B910",
    "B911",
    "B912",
    "B914",
    "B915",
    "B916",
    "B917",
    "B918",
    "B919",
    "B920",
    "B921",
    "B922",
    "B923",
    "B924",
    "B925",
    "B927",
    "B928",
    "B929",
    "B932",
    "B933",
    "B934",
    "B935",
    "B936",
    "B937",
    "B938",
    "B939",
    "B940",
    "B941",
    "B943",
    "B945",
    "B946",
    "B947",
    "B948",
    "B949",
    "B950",
    "B952",
    "B953",
    "B954",
    "B955",
    "B956",
    "B958",
    "B959",
    "B960",
    "B961",
    "B962",
    "B963",
    "B965",
    "B966",
    "B967",
    "B968",
    "B969",
    "B971",
    "B974",
    "B977",
    "B978",
    "B979",
    "B980",
    "B982",
    "B984",
    "B985",
    "B987",
    "B988",
    "B989",
    "B990",
    "B991",
    "B993",
    "B994",
    "B996",
    "B997",
    "B998",
    "B999",
    "C002",
    "C003",
    "C004",
    "C006",
    "C007",
    "C013",
    "C014",
    "C020",
    "C022",
    "C024",
    "C027",
    "C030",
    "C033",
    "C034",
    "C037",
    "C038",
    "C040",
    "C041",
    "C044",
    "C045",
    "C046",
    "C047",
    "C048",
    "C049",
    "C050",
    "C051",
    "C052",
    "C053",
    "C055",
    "C056",
    "C057",
    "C058",
    "C059",
    "C060",
    "C062",
    "C063",
    "C064",
    "C065",
    "C066",
    "C067",
    "C069",
    "C070",
    "C072",
    "C073",
    "C074",
    "C075",
    "C076",
    "C078",
    "C079",
    "C080",
    "C081",
    "C082",
    "C083",
    "C085",
    "C086",
    "C089",
    "C090",
    "C091",
    "C093",
    "C094",
    "C096",
    "C097",
    "C098",
    "C100",
    "C101",
    "C102",
    "C104",
    "C105",
    "C106",
    "C107",
    "C108",
    "C110",
    "C111",
    "C113",
    "C114",
    "C115",
    "C116",
    "C117",
    "C118",
    "C119",
    "C120",
    "C121",
    "C122",
    "C123",
    "C125",
    "C126",
    "C127",
    "C128",
    "C129",
    "C130",
    "C133",
    "C134",
    "C135",
    "C136",
    "C137",
    "C139",
    "C141",
    "C142",
    "C143",
    "C145",
    "C147",
    "C148",
    "C149",
    "C152",
    "C153",
    "C154",
    "C155",
    "C156",
    "C157",
    "C158",
    "C160",
    "C161",
    "C162",
    "C165",
    "C166",
    "C167",
    "C169",
    "C172",
    "C173",
    "C174",
    "C175",
    "C176",
    "C177",
    "C178",
    "C181",
    "C183",
    "C184",
    "C185",
    "C186",
    "C187",
    "C188",
    "C189",
    "C190",
    "C194",
    "C195",
    "C197",
    "C198",
    "C199",
    "C200",
    "C201",
    "C202",
    "C203",
    "C204",
    "C205",
    "C206",
    "C207",
    "C208",
    "C209",
    "C210",
    "C211",
    "C213",
    "C214",
    "C215",
    "C216",
    "C217",
    "C218",
    "C219",
    "C220",
    "C222",
    "C223",
    "C224",
    "C225",
    "C226",
    "C227",
    "C228",
    "C229",
    "C230",
    "C231",
    "C232",
    "C235",
    "C236",
    "C237",
    "C240",
    "C241",
    "C242",
    "C243",
    "C244",
    "C245",
    "C246",
    "C247",
    "C248",
    "C250",
    "C251",
    "C252",
    "C253",
    "C254",
    "C255",
    "C259",
    "C261",
    "C262",
    "C263",
    "C265",
    "C266",
    "C267",
    "C268",
    "C269",
    "C270",
    "C271",
    "C272",
    "C273",
    "C274",
    "C275",
    "C276",
    "C278",
    "C279",
    "C280",
    "C282",
    "C283",
    "C284",
    "C285",
    "C286",
    "C287",
    "C288",
    "C289",
    "C290",
    "C291",
    "C292",
    "C293",
    "C294",
    "C296",
    "C297",
    "C298",
    "C300",
    "C301",
    "C302",
    "C303",
    "C304",
    "C306",
    "C307",
    "C308",
    "C309",
    "C310",
    "C311",
    "C312",
    "C313",
    "C314",
    "C315",
    "C316",
    "C317",
    "C318",
    "C319",
    "C321",
    "C322",
    "C323",
    "C324",
    "C325",
    "C327",
    "C329",
    "C330",
    "C331",
    "C332",
    "C334",
    "C335",
    "C336",
    "C337",
    "C338",
    "C339",
    "C340",
    "C341",
    "C342",
    "C343",
    "C344",
    "C345",
    "C346",
    "C347",
    "C348",
    "C349",
    "C351",
    "C352",
    "C353",
    "C354",
    "C356",
    "C357",
    "C359",
    "C360",
    "C361",
    "C363",
    "C364",
    "C365",
    "C369",
    "C370",
    "C372",
    "C375",
    "C376",
    "C377",
    "C378",
    "C380",
    "C381",
    "C382",
    "C383",
    "C384",
    "C385",
    "C387",
    "C388",
    "C389",
    "C390",
    "C392",
    "C393",
    "C394",
    "C395",
    "C396",
    "C398",
    "C400",
    "C404",
    "C405",
    "C406",
    "C407",
    "C408",
    "C409",
    "C410",
    "C412",
    "C413",
    "C414",
    "C415",
    "C417",
    "C418",
    "C420",
    "C421",
    "C422",
    "C424",
    "C426",
    "C428",
    "C429",
    "C430",
    "C432",
    "C435",
    "C436",
    "C437",
    "C438",
    "C439",
    "C440",
    "C441",
    "C442",
    "C443",
    "C444",
    "C446",
    "C447",
    "C448",
    "C449",
    "C453",
    "C456",
    "C457",
    "C458",
    "C459",
    "C461",
    "C463",
    "C466",
    "C469",
    "C470",
    "C471",
    "C472",
    "C474",
    "C476",
    "C478",
    "C479",
    "C480",
    "C481",
    "C482",
    "C483",
    "C484",
    "C485",
    "C486",
    "C487",
    "C488",
    "C489",
    "C492",
    "C493",
    "C494",
    "C495",
    "C496",
    "C497",
    "C498",
    "C500",
    "C501",
    "C502",
    "C503",
    "C504",
    "C505",
    "C506",
    "C507",
    "C508",
    "C509",
    "C510",
    "C511",
    "C512",
    "C513",
    "C514",
    "C515",
    "C516",
    "C517",
    "C518",
    "C520",
    "C521",
    "C523",
    "C524",
    "C525",
    "C527",
    "C528",
    "C529",
    "C530",
    "C531",
    "C532",
    "C533",
    "C534",
    "C536",
    "C537",
    "C538",
    "C539",
    "C540",
    "C541",
    "C542",
    "C543",
    "C544",
    "C545",
    "C547",
    "C548",
    "C549",
    "C550",
    "C551",
    "C552",
    "C553",
    "C554",
    "C555",
    "C556",
    "C557",
    "C558",
    "C559",
    "C560",
    "C561",
    "C563",
    "C564",
    "C565",
    "C566",
    "C567",
    "C568",
    "C569",
    "C573",
    "C574",
    "C576",
    "C577",
    "C578",
    "C580",
    "C581",
    "C582",
    "C583",
    "C584",
    "C585",
    "C587",
    "C588",
    "C589",
    "C591",
    "C593",
    "C594",
    "C595",
    "C596",
    "C598",
    "C599",
    "C600",
    "C604",
    "C605",
    "C606",
    "C608",
    "C609",
    "C610",
    "C612",
    "C613",
    "C614",
    "C615",
    "C616",
    "C618",
    "C619",
    "C620",
    "C621",
    "C623",
    "C624",
    "C625",
    "C627",
    "C628",
    "C629",
    "C630",
    "C631",
    "C632",
    "C633",
    "C634",
    "C635",
    "C637",
    "C638",
    "C639",
    "C640",
    "C641",
    "C648",
    "C649",
    "C650",
    "C651",
    "C652",
    "C653",
    "C654",
    "C655",
    "C656",
    "C657",
    "C658",
    "C659",
    "C660",
    "C661",
    "C662",
    "C663",
    "C665",
    "C668",
    "C669",
    "C670",
    "C672",
    "C673",
    "C674",
    "C675",
    "C676",
    "C677",
    "C678",
    "C679",
    "C680",
    "C681",
    "C684",
    "C685",
    "C686",
    "C689",
    "C691",
    "C695",
    "C696",
    "C697",
    "C698",
    "C699",
    "C700",
    "C701",
    "C702",
    "C703",
    "C704",
    "C705",
    "C707",
    "C708",
    "C709",
    "C710",
    "C711",
    "C712",
    "C713",
    "C714",
    "C715",
    "C716",
    "C717",
    "C718",
    "C719",
    "C722",
    "C723",
    "C724",
    "C725",
    "C726",
    "C727",
    "C728",
    "C729",
    "C730",
    "C732",
    "C733",
    "C735",
    "C738",
    "C739",
    "C740",
    "C741",
    "C742",
    "C743",
    "C744",
    "C745",
    "C746",
    "C747",
    "C749",
    "C750",
    "C751",
    "C752",
    "C755",
    "C756",
    "C757",
    "C758",
    "C759",
    "C760",
    "C763",
    "C764",
    "C765",
    "C766",
    "C767",
    "C768",
    "C769",
    "C770",
    "C771",
    "C772",
    "C773",
    "C774",
    "C776",
    "C777",
    "C778",
    "C779",
    "C780",
    "C781",
    "C782",
    "C783",
    "C784",
    "C785",
    "C787",
    "C789",
    "C790",
    "C791",
    "C792",
    "C793",
    "C794",
    "C795",
    "C796",
    "C800",
    "C801",
    "C803",
    "C804",
    "C806",
    "C807",
    "C810",
    "C811",
    "C812",
    "C813",
    "C814",
    "C815",
    "C816",
    "C817",
    "C818",
    "C819",
    "C820",
    "C821",
    "C823",
    "C824",
    "C826",
    "C829",
    "C835",
    "C836",
    "C838",
    "C839",
    "C841",
    "C844",
    "C845",
    "C846",
    "C847",
    "C848",
    "C850",
    "C851",
    "C852",
    "C853",
    "C854",
    "C855",
    "C856",
    "C857",
    "C858",
    "C859",
    "C860",
    "C862",
    "C864",
    "C865",
    "C866",
    "C867",
    "C868",
    "C869",
    "C870",
    "C871",
    "C872",
    "C875",
    "C876",
    "C877",
    "C878",
    "C879",
    "C880",
    "C882",
    "C883",
    "C884",
    "C885",
    "C886",
    "C888",
    "C890",
    "C893",
    "C894",
    "C895",
    "C897",
    "C900",
    "C901",
    "C902",
    "C903",
    "C904",
    "C905",
    "C908",
    "C910",
    "C911",
    "C912",
    "C914",
    "C917",
    "C918",
    "C919",
    "C920",
    "C922",
    "C925",
    "C926",
    "C927",
    "C928",
    "C929",
    "C930",
    "C931",
    "C933",
    "C934",
    "C935",
    "C936",
    "C937",
    "C938",
    "C939",
    "C940",
    "C941",
    "C943",
    "C946",
    "C948",
    "C950",
    "C951",
    "C952",
    "C954",
    "C955",
    "C956",
    "C957",
    "C958",
    "C959",
    "C960",
    "C962",
    "C963",
    "C964",
    "C968",
    "C969",
    "C971",
    "C972",
    "C973",
    "C974",
    "C975",
    "C976",
    "C977",
    "C978",
    "C979",
    "C980",
    "C982",
    "C983",
    "C984",
    "C986",
    "C987",
    "C988",
    "C990",
    "C991",
    "C992",
    "C993",
    "C995",
    "C996",
    "C998",
    "C999",
    "D003",
    "D004",
    "D006",
    "D007",
    "D008",
    "D009",
    "D010",
    "D011",
    "D012",
    "D013",
    "D014",
    "D015",
    "D016",
    "D018",
    "D019",
    "D020",
    "D021",
    "D022",
    "D024",
    "D026",
    "D027",
    "D028",
    "D029",
    "D030",
    "D033",
    "D037",
    "D038",
    "D040",
    "D041",
    "D042",
    "D043",
    "D044",
    "D045",
    "D046",
    "D048",
    "D049",
    "D050",
    "D051",
    "D052",
    "D054",
    "D056",
    "D057",
    "D058",
    "D061",
    "D062",
    "D064",
    "D065",
    "D066",
    "D068",
    "D072",
    "D075",
    "D076",
    "D077",
    "D078",
    "D079",
    "D081",
    "D082",
    "D085",
    "D086",
    "D087",
    "D088",
    "D089",
    "D092",
    "D093",
    "D094",
    "D095",
    "D096",
    "D099",
    "D100",
    "D101",
    "D102",
    "D103",
    "D105",
    "D107",
    "D108",
    "D109",
    "D110",
    "D111",
    "D112",
    "D113",
    "D114",
    "D117",
    "D118",
    "D119",
    "D120",
    "D121",
    "D122",
    "D123",
    "D124",
    "D126",
    "D127",
    "D128",
    "D131",
    "D132",
    "D133",
    "D134",
    "D136",
    "D137",
    "D139",
    "D141",
    "D142",
    "D143",
    "D144",
    "D145",
    "D147",
    "D149",
    "D150",
    "D151",
    "D154",
    "D156",
    "D159",
    "D161",
    "D162",
    "D165",
    "D166",
    "D168",
    "D170",
    "D171",
    "D172",
    "D175",
    "D177",
    "D179",
    "D180",
    "D181",
    "D184",
    "D185",
    "D186",
    "D187",
    "D188",
    "D189",
    "D192",
    "D193",
    "D195",
    "D196",
    "D197",
    "D198",
    "D199",
    "D200",
    "D201",
    "D202",
    "D203",
    "D204",
    "D205",
    "D207",
    "D208",
    "D209",
    "D210",
    "D211",
    "D214",
    "D216",
    "D217",
    "D218",
    "D219",
    "D221",
    "D222",
    "D223",
    "D226",
    "D227",
    "D228",
    "D229",
    "D230",
    "D231",
    "D232",
    "D233",
    "D234",
    "D236",
    "D237",
    "D238",
    "D239",
    "D244",
    "D245",
    "D246",
    "D247",
    "D251",
    "D253",
    "D255",
    "D256",
    "D257",
    "D258",
    "D259",
    "D260",
    "D261",
    "D264",
    "D265",
    "D266",
    "D267",
    "D268",
    "D269",
    "D270",
    "D271",
    "D272",
    "D273",
    "D277",
    "D278",
    "D279",
    "D280",
    "D281",
    "D284",
    "D286",
    "D287",
    "D289",
    "D290",
    "D291",
    "D292",
    "D293",
    "D296",
    "D297",
    "D298",
    "D299",
    "D300",
    "D303",
    "D304",
    "D305",
    "D309",
    "D310",
    "D311",
    "D312",
    "D314",
    "D315",
    "D316",
    "D317",
    "D318",
    "D319",
    "D321",
    "D323",
    "D324",
    "D325",
    "D327",
    "D328",
    "D329",
    "D330",
    "D331",
    "D332",
    "D333",
    "D334",
    "D338",
    "D339",
    "D341",
    "D344",
    "D345",
    "D346",
    "D347",
    "D348",
    "D350",
    "D351",
    "D352",
    "D355",
    "D356",
    "D357",
    "D358",
    "D360",
    "D361",
    "D364",
    "D365",
    "D366",
    "D367",
    "D371",
    "D372",
    "D373",
    "D374",
    "D376",
    "D377",
    "D379",
    "D380",
    "D383",
    "D384",
    "D385",
    "D386",
    "D388",
    "D390",
    "D391",
    "D392",
    "D394",
    "D395",
    "D398",
    "D399",
    "D401",
    "D402",
    "D403",
    "D406",
    "D407",
    "D408",
    "D410",
    "D411",
    "D412",
    "D414",
    "D415",
    "D416",
    "D419",
    "D420",
    "D421",
    "D422",
    "D423",
    "D424",
    "D426",
    "D428",
    "D429",
    "D430",
    "D431",
    "D433",
    "D434",
    "D436",
    "D440",
    "D441",
    "D442",
    "D443",
    "D444",
    "D445",
    "D447",
    "D450",
    "D451",
    "D452",
    "D453",
    "D454",
    "D455",
    "D456",
    "D458",
    "D459",
    "D461",
    "D462",
    "D463",
    "D464",
    "D465",
    "D467",
    "D468",
    "D469",
    "D470",
    "D471",
    "D472",
    "D473",
    "D474",
    "D475",
    "D476",
    "D477",
    "D480",
    "D482",
    "D483",
    "D484",
    "D486",
    "D487",
    "D488",
    "D489",
    "D490",
    "D491",
    "D492",
    "D493",
    "D494",
    "D495",
    "D496",
    "D497",
    "D499",
    "D501",
    "D502",
    "D503",
    "D504",
    "D505",
    "D508",
    "D509",
    "D510",
    "D511",
    "D512",
    "D513",
    "D514",
    "D518",
    "D520",
    "D522",
    "D523",
    "D524",
    "D526",
    "D527",
    "D528",
    "D530",
    "D531",
    "D532",
    "D537",
    "D538",
    "D539",
    "D540",
    "D541",
    "D542",
    "D543",
    "D544",
    "D545",
    "D546",
    "D547",
    "D548",
    "D549",
    "D550",
    "D551",
    "D552",
    "D553",
    "D554",
    "D555",
    "D557",
    "D559",
    "D560",
    "D561",
    "D562",
    "D564",
    "D565",
    "D566",
    "D567",
    "D568",
    "D569",
    "D570",
    "D571",
    "D573",
    "D574",
    "D575",
    "D576",
    "D577",
    "D578",
    "D579",
    "D582",
    "D585",
    "D586",
    "D587",
    "D588",
    "D589",
    "D590",
    "D591",
    "D592",
    "D593",
    "D594",
    "D595",
    "D596",
    "D597",
    "D599",
    "D600",
    "D604",
    "D605",
    "D606",
    "D607",
    "D608",
    "D611",
    "D612",
    "D613",
    "D614",
    "D615",
    "D617",
    "D619",
    "D621",
    "D622",
    "D623",
    "D624",
    "D628",
    "D629",
    "D630",
    "D634",
    "D635",
    "D636",
    "D637",
    "D638",
    "D639",
    "D640",
    "D641",
    "D643",
    "D644",
    "D645",
    "D646",
    "D649",
    "D650",
    "D651",
    "D652",
    "D653",
    "D654",
    "D655",
    "D656",
    "D660",
    "D661",
    "D662",
    "D665",
    "D666",
    "D667",
    "D668",
    "D670",
    "D671",
    "D672",
    "D673",
    "D674",
    "D675",
    "D676",
    "D677",
    "D678",
    "D679",
    "D680",
    "D681",
    "D682",
    "D683",
    "D684",
    "D685",
    "D686",
    "D688",
    "D689",
    "D690",
    "D691",
    "D693",
    "D694",
    "D695",
    "D696",
    "D697",
    "D700",
    "D701",
    "D702",
    "D703",
    "D704",
    "D705",
    "D706",
    "D707",
    "D708",
    "D709",
    "D710",
    "D711",
    "D712",
    "D714",
    "D715",
    "D717",
    "D718",
    "D719",
    "D720",
    "D725",
    "D727",
    "D728",
    "D730",
    "D731",
    "D732",
    "D733",
    "D734",
    "D735",
    "D736",
    "D737",
    "D738",
    "D740",
    "D741",
    "D742",
    "D744",
    "D745",
    "D746",
    "D748",
    "D749",
    "D750",
    "D751",
    "D752",
    "D754",
    "D755",
    "D756",
    "D757",
    "D758",
    "D759",
    "D760",
    "D761",
    "D762",
    "D763",
    "D764",
    "D765",
    "D766",
    "D767",
    "D768",
    "D769",
    "D770",
    "D771",
    "D773",
    "D774",
    "D775",
    "D776",
    "D777",
    "D780",
    "D781",
    "D782",
    "D783",
    "D784",
    "D785",
    "D786",
    "D787",
    "D788",
    "D789",
    "D790",
    "D791",
    "D793",
    "D794",
    "D796",
    "D797",
    "D798",
    "D799",
    "D801",
    "D802",
    "D803",
    "D804",
    "D805",
    "D807",
    "D808",
    "D810",
    "D811",
    "D812",
    "D813",
    "D814",
    "D815",
    "D817",
    "D818",
    "D819",
    "D821",
    "D823",
    "D824",
    "D825",
    "D826",
    "D827",
    "D828",
    "D829",
    "D830",
    "D832",
    "D834",
    "D835",
    "D836",
    "D839",
    "D841",
    "D842",
    "D843",
    "D844",
    "D845",
    "D847",
    "D848",
    "D849",
    "D850",
    "D851",
    "D852",
    "D853",
    "D854",
    "D855",
    "D856",
    "D858",
    "D859",
    "D860",
    "D861",
    "D862",
    "D863",
    "D864",
    "D865",
    "D867",
    "D868",
    "D869",
    "D870",
    "D871",
    "D872",
    "D873",
    "D874",
    "D875",
    "D876",
    "D878",
    "D879",
    "D881",
    "D882",
    "D883",
    "D884",
    "D885",
    "D886",
    "D888",
    "D889",
    "D890",
    "D891",
    "D892",
    "D894",
    "D895",
    "D896",
    "D897",
    "D898",
    "D899",
    "D901",
    "D902",
    "D903",
    "D905",
    "D906",
    "D907",
    "D909",
    "D910",
    "D911",
    "D912",
    "D913",
    "D915",
    "D917",
    "D918",
    "D920",
    "D921",
    "D923",
    "D924",
    "D925",
    "D926",
    "D927",
    "D928",
    "D930",
    "D931",
    "D932",
    "D933",
    "D934",
    "D935",
    "D938",
    "D940",
    "D942",
    "D943",
    "D944",
    "D945",
    "D946",
    "D947",
    "D948",
    "D949",
    "D951",
    "D952",
    "D956",
    "D957",
    "D958",
    "D959",
    "D960",
    "D961",
    "D962",
    "D963",
    "D964",
    "D965",
    "D966",
    "D967",
    "D968",
    "D969",
    "D970",
    "D971",
    "D972",
    "D974",
    "D975",
    "D976",
    "D977",
    "D978",
    "D980",
    "D981",
    "D982",
    "D983",
    "D984",
    "D987",
    "D988",
    "D990",
    "D993",
    "D994",
    "D995",
    "D996",
    "D997",
    "D998",
    "D999",
    "E001",
    "E003",
    "E004",
    "E006",
    "E007",
    "E008",
    "E009",
    "E010",
    "E011",
    "E012",
    "E013",
    "E014",
    "E015",
    "E016",
    "E017",
    "E019",
    "E020",
    "E021",
    "E022",
    "E023",
    "E024",
    "E025",
    "E026",
    "E027",
    "E028",
    "E029",
    "E030",
    "E031",
    "E033",
    "E034",
    "E036",
    "E037",
    "E038",
    "E039",
    "E040",
    "E041",
    "E043",
    "E044",
    "E045",
    "E047",
    "E048",
    "E049",
    "E050",
    "E052",
    "E053",
    "E054",
    "E055",
    "E056",
    "E057",
    "E058",
    "E060",
    "E061",
    "E062",
    "E063",
    "E064",
    "E065",
    "E066",
    "E067",
    "E068",
    "E069",
    "E070",
    "E071",
    "E072",
    "E074",
    "E078",
    "E079",
    "E081",
    "E082",
    "E083",
    "E084",
    "E085",
    "E086",
    "E087",
    "E088",
    "E089",
    "E090",
    "E091",
    "E092",
    "E093",
    "E094",
    "E096",
    "E098",
    "E100",
    "E101",
    "E102",
    "E103",
    "E104",
    "E106",
    "E107",
    "E109",
    "E111",
    "E113",
    "E114",
    "E115",
    "E116",
    "E118",
    "E120",
    "E122",
    "E124",
    "E125",
    "E126",
    "E127",
    "E128",
    "E130",
    "E131",
    "E132",
    "E133",
    "E134",
    "E136",
    "E139",
    "E141",
    "E142",
    "E143",
    "E144",
    "E145",
    "E146",
    "E147",
    "E148",
    "E149",
    "E152",
    "E153",
    "E154",
    "E155",
    "E156",
    "E158",
    "E159",
    "E160",
    "E161",
    "E163",
    "E164",
    "E165",
    "E167",
    "E168",
    "E169",
    "E170",
    "E171",
    "E172",
    "E173",
    "E177",
    "E178",
    "E179",
    "E180",
    "E182",
    "E184",
    "E185",
    "E187",
    "E188",
    "E189",
    "E191",
    "E192",
    "E193",
    "E195",
    "E196",
    "E199",
    "E200",
    "E201",
    "E202",
    "E203",
    "E204",
    "E205",
    "E206",
    "E207",
    "E208",
    "E209",
    "E210",
    "E212",
    "E213",
    "E214",
    "E215",
    "E216",
    "E217",
    "E219",
    "E221",
    "E223",
    "E224",
    "E226",
    "E227",
    "E228",
    "E229",
    "E230",
    "E232",
    "E233",
    "E234",
    "E235",
    "E236",
    "E237",
    "E238",
    "E239",
    "E240",
    "E241",
    "E242",
    "E243",
    "E244",
    "E245",
    "E246",
    "E248",
    "E249",
    "E250",
    "E251",
    "E252",
    "E253",
    "E255",
    "E256",
    "E258",
    "E259",
    "E261",
    "E263",
    "E264",
    "E265",
    "E266",
    "E269",
    "E270",
    "E271",
    "E272",
    "E273",
    "E274",
    "E280",
    "E281",
    "E282",
    "E283",
    "E284",
    "E285",
    "E287",
    "E288",
    "E289",
    "E290",
    "E291",
    "E292",
    "E295",
    "E297",
    "E299",
    "E301",
    "E304",
    "E305",
    "E306",
    "E307",
    "E309",
    "E310",
    "E311",
    "E313",
    "E314",
    "E317",
    "E320",
    "E321",
    "E323",
    "E325",
    "E326",
    "E327",
    "E328",
    "E329",
    "E330",
    "E332",
    "E333",
    "E334",
    "E335",
    "E336",
    "E337",
    "E338",
    "E339",
    "E340",
    "E341",
    "E342",
    "E343",
    "E345",
    "E346",
    "E348",
    "E349",
    "E350",
    "E351",
    "E353",
    "E354",
    "E356",
    "E358",
    "E360",
    "E363",
    "E364",
    "E365",
    "E366",
    "E367",
    "E368",
    "E369",
    "E370",
    "E371",
    "E372",
    "E373",
    "E374",
    "E375",
    "E376",
    "E377",
    "E379",
    "E380",
    "E381",
    "E382",
    "E386",
    "E387",
    "E388",
    "E389",
    "E390",
    "E391",
    "E392",
    "E393",
    "E394",
    "E395",
    "E396",
    "E397",
    "E398",
    "E400",
    "E401",
    "E402",
    "E403",
    "E405",
    "E406",
    "E407",
    "E409",
    "E410",
    "E412",
    "E413",
    "E414",
    "E415",
    "E416",
    "E417",
    "E419",
    "E420",
    "E421",
    "E422",
    "E423",
    "E424",
    "E425",
    "E426",
    "E428",
    "E429",
    "E430",
    "E431",
    "E432",
    "E433",
    "E434",
    "E435",
    "E436",
    "E437",
    "E438",
    "E439",
    "E441",
    "E443",
    "E445",
    "E447",
    "E448",
    "E450",
    "E451",
    "E454",
    "E456",
    "E457",
    "E458",
    "E459",
    "E462",
    "E463",
    "E464",
    "E465",
    "E466",
    "E467",
    "E469",
    "E470",
    "E471",
    "E472",
    "E473",
    "E474",
    "E475",
    "E476",
    "E479",
    "E480",
    "E481",
    "E482",
    "E483",
    "E484",
    "E485",
    "E486",
    "E487",
    "E488",
    "E489",
    "E490",
    "E491",
    "E492",
    "E493",
    "E494",
    "E496",
    "E497",
    "E498",
    "E500",
    "E502",
    "E504",
    "E505",
    "E506",
    "E507",
    "E509",
    "E510",
    "E512",
    "E514",
    "E515",
    "E517",
    "E518",
    "E519",
    "E520",
    "E522",
    "E523",
    "E524",
    "E526",
    "E527",
    "E528",
    "E530",
    "E531",
    "E532",
    "E535",
    "E536",
    "E537",
    "E538",
    "E539",
    "E540",
    "E541",
    "E542",
    "E543",
    "E544",
    "E546",
    "E547",
    "E548",
    "E549",
    "E550",
    "E551",
    "E553",
    "E554",
    "E555",
    "E557",
    "E558",
    "E559",
    "E560",
    "E562",
    "E563",
    "E564",
    "E565",
    "E566",
    "E569",
    "E570",
    "E571",
    "E573",
    "E574",
    "E576",
    "E578",
    "E581",
    "E583",
    "E584",
    "E587",
    "E588",
    "E589",
    "E590",
    "E591",
    "E592",
    "E593",
    "E594",
    "E596",
    "E597",
    "E599",
    "E600",
    "E602",
    "E605",
    "E606",
    "E607",
    "E608",
    "E610",
    "E611",
    "E613",
    "E615",
    "E617",
    "E618",
    "E620",
    "E621",
    "E622",
    "E623",
    "E624",
    "E625",
    "E626",
    "E627",
    "E629",
    "E630",
    "E632",
    "E633",
    "E635",
    "E638",
    "E639",
    "E640",
    "E644",
    "E645",
    "E646",
    "E647",
    "E648",
    "E649",
    "E651",
    "E652",
    "E654",
    "E655",
    "E656",
    "E659",
    "E660",
    "E661",
    "E662",
    "E664",
    "E665",
    "E666",
    "E668",
    "E669",
    "E671",
    "E673",
    "E674",
    "E675",
    "E677",
    "E678",
    "E679",
    "E680",
    "E681",
    "E682",
    "E683",
    "E684",
    "E685",
    "E687",
    "E689",
    "E690",
    "E691",
    "E692",
    "E693",
    "E694",
    "E695",
    "E698",
    "E700",
    "E704",
    "E705",
    "E706",
    "E707",
    "E708",
    "E709",
    "E711",
    "E713",
    "E714",
    "E715",
    "E716",
    "E718",
    "E719",
    "E722",
    "E723",
    "E724",
    "E726",
    "E729",
    "E730",
    "E731",
    "E734",
    "E735",
    "E736",
    "E737",
    "E738",
    "E742",
    "E743",
    "E745",
    "E746",
    "E747",
    "E748",
    "E749",
    "E750",
    "E751",
    "E752",
    "E753",
    "E754",
    "E757",
    "E758",
    "E759",
    "E760",
    "E761",
    "E763",
    "E764",
    "E767",
    "E769",
    "E770",
    "E772",
    "E773",
    "E777",
    "E778",
    "E779",
    "E780",
    "E782",
    "E783",
    "E784",
    "E785",
    "E786",
    "E787",
    "E788",
    "E789",
    "E790",
    "E791",
    "E793",
    "E794",
    "E795",
    "E798",
    "E799",
    "E800",
    "E801",
    "E803",
    "E804",
    "E805",
    "E806",
    "E807",
    "E808",
    "E809",
    "E810",
    "E811",
    "E812",
    "E813",
    "E814",
    "E815",
    "E816",
    "E817",
    "E818",
    "E819",
    "E820",
    "E821",
    "E825",
    "E829",
    "E830",
    "E833",
    "E834",
    "E835",
    "E836",
    "E837",
    "E838",
    "E839",
    "E840",
    "E841",
    "E842",
    "E843",
    "E844",
    "E847",
    "E848",
    "E850",
    "E851",
    "E852",
    "E853",
    "E854",
    "E855",
    "E856",
    "E858",
    "E859",
    "E860",
    "E862",
    "E863",
    "E864",
    "E865",
    "E868",
    "E869",
    "E870",
    "E872",
    "E873",
    "E874",
    "E875",
    "E876",
    "E877",
    "E878",
    "E879",
    "E880",
    "E882",
    "E883",
    "E884",
    "E885",
    "E887",
    "E888",
    "E889",
    "E891",
    "E892",
    "E893",
    "E894",
    "E896",
    "E897",
    "E899",
    "E900",
    "E901",
    "E902",
    "E903",
    "E904",
    "E905",
    "E906",
    "E907",
    "E908",
    "E910",
    "E911",
    "E912",
    "E914",
    "E915",
    "E917",
    "E919",
    "E921",
    "E922",
    "E923",
    "E924",
    "E925",
    "E927",
    "E928",
    "E929",
    "E930",
    "E931",
    "E932",
    "E933",
    "E934",
    "E936",
    "E938",
    "E939",
    "E940",
    "E941",
    "E944",
    "E945",
    "E946",
    "E947",
    "E949",
    "E951",
    "E952",
    "E953",
    "E954",
    "E955",
    "E956",
    "E957",
    "E958",
    "E959",
    "E960",
    "E961",
    "E962",
    "E963",
    "E965",
    "E967",
    "E968",
    "E970",
    "E971",
    "E972",
    "E973",
    "E974",
    "E975",
    "E976",
    "E977",
    "E978",
    "E979",
    "E980",
    "E981",
    "E982",
    "E983",
    "E984",
    "E986",
    "E987",
    "E988",
    "E989",
    "E990",
    "E991",
    "E992",
    "E993",
    "E994",
    "E995",
    "E997",
    "E998",
    "E999",
    "F001",
    "F002",
    "F003",
    "F004",
    "F005",
    "F006",
    "F007",
    "F009",
    "F010",
    "F011",
    "F012",
    "F013",
    "F015",
    "F016",
    "F017",
    "F020",
    "F021",
    "F022",
    "F023",
    "F024",
    "F025",
    "F027",
    "F028",
    "F029",
    "F030",
    "F032",
    "F033",
    "F035",
    "F037",
    "F041",
    "F042",
    "F043",
    "F044",
    "F045",
    "F046",
    "F047",
    "F048",
    "F050",
    "F051",
    "F052",
    "F053",
    "F054",
    "F055",
    "F058",
    "F059",
    "F061",
    "F063",
    "F064",
    "F065",
    "F066",
    "F067",
    "F068",
    "F070",
    "F073",
    "F074",
    "F078",
    "F080",
    "F081",
    "F082",
    "F083",
    "F084",
    "F085",
    "F086",
    "F087",
    "F088",
    "F089",
    "F092",
    "F093",
    "F095",
    "F096",
    "F097",
    "F098",
    "F100",
    "F101",
    "F102",
    "F104",
    "F105",
    "F106",
    "F107",
    "F108",
    "F109",
    "F110",
    "F111",
    "F112",
    "F113",
    "F114",
    "F115",
    "F116",
    "F117",
    "F118",
    "F119",
    "F120",
    "F122",
    "F123",
    "F125",
    "F126",
    "F127",
    "F130",
    "F131",
    "F132",
    "F133",
    "F134",
    "F135",
    "F136",
    "F137",
    "F138",
    "F139",
    "F140",
    "F141",
    "F144",
    "F145",
    "F146",
    "F147",
    "F148",
    "F149",
    "F151",
    "F152",
    "F153",
    "F154",
    "F155",
    "F156",
    "F157",
    "F158",
    "F161",
    "F162",
    "F165",
    "F167",
    "F168",
    "F170",
    "F171",
    "F172",
    "F173",
    "F175",
    "F176",
    "F182",
    "F183",
    "F184",
    "F186",
    "F187",
    "F188",
    "F189",
    "F190",
    "F191",
    "F192",
    "F193",
    "F194",
    "F196",
    "F200",
    "F201",
    "F202",
    "F203",
    "F205",
    "F206",
    "F207",
    "F208",
    "F209",
    "F210",
    "F213",
    "F214",
    "F216",
    "F217",
    "F218",
    "F219",
    "F220",
    "F221",
    "F223",
    "F224",
    "F225",
    "F226",
    "F229",
    "F230",
    "F231",
    "F232",
    "F233",
    "F238",
    "F239",
    "F240",
    "F241",
    "F242",
    "F243",
    "F244",
    "F246",
    "F247",
    "F248",
    "F249",
    "F250",
    "F251",
    "F254",
    "F256",
    "F257",
    "F258",
    "F259",
    "F260",
    "F261",
    "F262",
    "F263",
    "F265",
    "F266",
    "F267",
    "F268",
    "F269",
    "F270",
    "F271",
    "F272",
    "F274",
    "F275",
    "F276",
    "F277",
    "F278",
    "F279",
    "F280",
    "F281",
    "F283",
    "F284",
    "F287",
    "F288",
    "F290",
    "F293",
    "F294",
    "F295",
    "F297",
    "F299",
    "F301",
    "F304",
    "F305",
    "F307",
    "F308",
    "F309",
    "F310",
    "F311",
    "F312",
    "F313",
    "F315",
    "F316",
    "F317",
    "F318",
    "F319",
    "F320",
    "F322",
    "F323",
    "F324",
    "F325",
    "F326",
    "F327",
    "F328",
    "F329",
    "F330",
    "F332",
    "F333",
    "F335",
    "F336",
    "F337",
    "F338",
    "F340",
    "F342",
    "F343",
    "F346",
    "F347",
    "F348",
    "F351",
    "F352",
    "F354",
    "F355",
    "F356",
    "F357",
    "F358",
    "F359",
    "F360",
    "F361",
    "F363",
    "F364",
    "F365",
    "F367",
    "F368",
    "F369",
    "F370",
    "F371",
    "F372",
    "F373",
    "F374",
    "F375",
    "F376",
    "F377",
    "F378",
    "F379",
    "F380",
    "F381",
    "F382",
    "F383",
    "F384",
    "F385",
    "F386",
    "F387",
    "F390",
    "F391",
    "F392",
    "F393",
    "F394",
    "F395",
    "F397",
    "F398",
    "F399",
    "F400",
    "F401",
    "F403",
    "F404",
    "F405",
    "F407",
    "F408",
    "F409",
    "F410",
    "F411",
    "F414",
    "F415",
    "F416",
    "F417",
    "F419",
    "F420",
    "F422",
    "F423",
    "F424",
    "F426",
    "F427",
    "F428",
    "F429",
    "F430",
    "F432",
    "F433",
    "F434",
    "F437",
    "F440",
    "F441",
    "F442",
    "F443",
    "F445",
    "F446",
    "F448",
    "F449",
    "F450",
    "F452",
    "F453",
    "F454",
    "F455",
    "F456",
    "F457",
    "F458",
    "F460",
    "F461",
    "F462",
    "F463",
    "F464",
    "F465",
    "F467",
    "F468",
    "F469",
    "F471",
    "F473",
    "F475",
    "F477",
    "F478",
    "F479",
    "F480",
    "F481",
    "F482",
    "F483",
    "F484",
    "F486",
    "F487",
    "F488",
    "F489",
    "F491",
    "F492",
    "F493",
    "F494",
    "F495",
    "F496",
    "F497",
    "F498",
    "F499",
    "F500",
    "F501",
    "F502",
    "F503",
    "F504",
    "F506",
    "F507",
    "F508",
    "F509",
    "F510",
    "F511",
    "F512",
    "F513",
    "F514",
    "F515",
    "F516",
    "F517",
    "F518",
    "F519",
    "F520",
    "F522",
    "F523",
    "F524",
    "F526",
    "F527",
    "F528",
    "F529",
    "F531",
    "F532",
    "F533",
    "F534",
    "F535",
    "F536",
    "F537",
    "F538",
    "F540",
    "F541",
    "F542",
    "F543",
    "F544",
    "F545",
    "F546",
    "F547",
    "F548",
    "F549",
    "F550",
    "F551",
    "F552",
    "F553",
    "F556",
    "F557",
    "F558",
    "F559",
    "F560",
    "F561",
    "F562",
    "F563",
    "F564",
    "F565",
    "F566",
    "F567",
    "F568",
    "F569",
    "F570",
    "F572",
    "F573",
    "F574",
    "F576",
    "F578",
    "F579",
    "F580",
    "F581",
    "F582",
    "F585",
    "F586",
    "F587",
    "F589",
    "F590",
    "F591",
    "F592",
    "F594",
    "F595",
    "F596",
    "F597",
    "F598",
    "F599",
    "F600",
    "F601",
    "F603",
    "F604",
    "F605",
    "F606",
    "F607",
    "F608",
    "F609",
    "F610",
    "F611",
    "F612",
    "F614",
    "F616",
    "F618",
    "F619",
    "F620",
    "F621",
    "F622",
    "F623",
    "F625",
    "F626",
    "F627",
    "F628",
    "F629",
    "F631",
    "F632",
    "F634",
    "F636",
    "F637",
    "F638",
    "F639",
    "F640",
    "F642",
    "F644",
    "F646",
    "F648",
    "F651",
    "F653",
    "F654",
    "F655",
    "F656",
    "F657",
    "F660",
    "F661",
    "F662",
    "F664",
    "F665",
    "F666",
    "F667",
    "F668",
    "F669",
    "F670",
    "F671",
    "F672",
    "F674",
    "F675",
    "F676",
    "F677",
    "F679",
    "F680",
    "F681",
    "F682",
    "F685",
    "F686",
    "F687",
    "F688",
    "F689",
    "F690",
    "F692",
    "F696",
    "F697",
    "F698",
    "F701",
    "F703",
    "F704",
    "F705",
    "F706",
    "F707",
    "F708",
    "F709",
    "F710",
    "F711",
    "F712",
    "F713",
    "F715",
    "F716",
    "F717",
    "F718",
    "F720",
    "F721",
    "F722",
    "F723",
    "F724",
    "F725",
    "F726",
    "F727",
    "F728",
    "F729",
    "F730",
    "F731",
    "F732",
    "F733",
    "F734",
    "F735",
    "F736",
    "F737",
    "F738",
    "F739",
    "F740",
    "F743",
    "F744",
    "F745",
    "F746",
    "F747",
    "F748",
    "F749",
    "F750",
    "F751",
    "F754",
    "F756",
    "F758",
    "F760",
    "F761",
    "F762",
    "F764",
    "F765",
    "F766",
    "F767",
    "F770",
    "F771",
    "F772",
    "F773",
    "F774",
    "F775",
    "F776",
    "F777",
    "F779",
    "F780",
    "F781",
    "F783",
    "F784",
    "F785",
    "F786",
    "F788",
    "F789",
    "F791",
    "F793",
    "F795",
    "F797",
    "F798",
    "F799",
    "F801",
    "F802",
    "F806",
    "F808",
    "F809",
    "F810",
    "F811",
    "F813",
    "F814",
    "F815",
    "F816",
    "F817",
    "F818",
    "F820",
    "F822",
    "F826",
    "F828",
    "F829",
    "F830",
    "F831",
    "F832",
    "F833",
    "F835",
    "F836",
    "F838",
    "F839",
    "F840",
    "F841",
    "F842",
    "F843",
    "F844",
    "F845",
    "F846",
    "F847",
    "F848",
    "F849",
    "F851",
    "F852",
    "F856",
    "F857",
    "F858",
    "F859",
    "F861",
    "F862",
    "F863",
    "F864",
    "F865",
    "F866",
    "F867",
    "F868",
    "F870",
    "F871",
    "F872",
    "F874",
    "F876",
    "F877",
    "F878",
    "F880",
    "F881",
    "F882",
    "F883",
    "F884",
    "F886",
    "F887",
    "F889",
    "F890",
    "F891",
    "F892",
    "F893",
    "F894",
    "F895",
    "F898",
    "F899",
    "F900",
    "F901",
    "F902",
    "F904",
    "F906",
    "F907",
    "F908",
    "F910",
    "F911",
    "F912",
    "F913",
    "F914",
    "F915",
    "F916",
    "F917",
    "F918",
    "F920",
    "F921",
    "F922",
    "F923",
    "F924",
    "F925",
    "F926",
    "F927",
    "F929",
    "F930",
    "F931",
    "F932",
    "F933",
    "F934",
    "F935",
    "F937",
    "F939",
    "F941",
    "F942",
    "F943",
    "F944",
    "F947",
    "F948",
    "F949",
    "F950",
    "F951",
    "F952",
    "F955",
    "F956",
    "F957",
    "F958",
    "F960",
    "F961",
    "F962",
    "F963",
    "F964",
    "F965",
    "F966",
    "F967",
    "F968",
    "F970",
    "F972",
    "F974",
    "F975",
    "F976",
    "F977",
    "F978",
    "F979",
    "F980",
    "F981",
    "F982",
    "F983",
    "F985",
    "F986",
    "F987",
    "F988",
    "F989",
    "F990",
    "F991",
    "F992",
    "F993",
    "F994",
    "F995",
    "F996",
    "F997",
    "F998",
    "F999",
    "G001",
    "G002",
    "G003",
    "G004",
    "G005",
    "G006",
    "G007",
    "G008",
    "G009",
    "G010",
    "G011",
    "G012",
    "G015",
    "G016",
    "G018",
    "G019",
    "G020",
    "G021",
    "G022",
    "G023",
    "G025",
    "G026",
    "G028",
    "G030",
    "G031",
    "G032",
    "G034",
    "G036",
    "G037",
    "G039",
    "G040",
    "G041",
    "G042",
    "G043",
    "G044",
    "G045",
    "G046",
    "G047",
    "G048",
    "G049",
    "G050",
    "G054",
    "G056",
    "G058",
    "G061",
    "G062",
    "G063",
    "G064",
    "G065",
    "G066",
    "G068",
    "G070",
    "G071",
    "G074",
    "G075",
    "G076",
    "G078",
    "G079",
    "G080",
    "G081",
    "G082",
    "G083",
    "G084",
    "G086",
    "G087",
    "G088",
    "G090",
    "G093",
    "G095",
    "G096",
    "G097",
    "G098",
    "G102",
    "G103",
    "G105",
    "G107",
    "G108",
    "G109",
    "G110",
    "G111",
    "G113",
    "G114",
    "G115",
    "G116",
    "G117",
    "G118",
    "G119",
    "G120",
    "G121",
    "G122",
    "G123",
    "G124",
    "G125",
    "G126",
    "G128",
    "G129",
    "G130",
    "G131",
    "G133",
    "G134",
    "G135",
    "G136",
    "G137",
    "G139",
    "G140",
    "G141",
    "G142",
    "G143",
    "G144",
    "G145",
    "G146",
    "G147",
    "G148",
    "G149",
    "G150",
    "G151",
    "G152",
    "G153",
    "G154",
    "G155",
    "G156",
    "G157",
    "G158",
    "G159",
    "G160",
    "G161",
    "G163",
    "G164",
    "G165",
    "G166",
    "G167",
    "G168",
    "G169",
    "G170",
    "G171",
    "G173",
    "G178",
    "G179",
    "G181",
    "G183",
    "G184",
    "G185",
    "G186",
    "G187",
    "G188",
    "G189",
    "G190",
    "G191",
    "G192",
    "G193",
    "G194",
    "G195",
    "G196",
    "G197",
    "G198",
    "G199",
    "G200",
    "G201",
    "G202",
    "G203",
    "G204",
    "G205",
    "G206",
    "G207",
    "G208",
    "G209",
    "G210",
    "G211",
    "G212",
    "G213",
    "G215",
    "G217",
    "G218",
    "G220",
    "G222",
    "G223",
    "G224",
    "G225",
    "G226",
    "G227",
    "G228",
    "G229",
    "G230",
    "G232",
    "G233",
    "G234",
    "G237",
    "G238",
    "G240",
    "G241",
    "G242",
    "G243",
    "G247",
    "G248",
    "G249",
    "G250",
    "G251",
    "G252",
    "G253",
    "G254",
    "G255",
    "G257",
    "G258",
    "G259",
    "G260",
    "G261",
    "G262",
    "G263",
    "G264",
    "G266",
    "G267",
    "G268",
    "G270",
    "G271",
    "G272",
    "G273",
    "G274",
    "G275",
    "G276",
    "G277",
    "G278",
    "G280",
    "G281",
    "G282",
    "G283",
    "G284",
    "G285",
    "G286",
    "G287",
    "G288",
    "G289",
    "G290",
    "G291",
    "G292",
    "G293",
    "G294",
    "G295",
    "G296",
    "G297",
    "G298",
    "G299",
    "G300",
    "G302",
    "G303",
    "G304",
    "G305",
    "G306",
    "G307",
    "G308",
    "G309",
    "G311",
    "G312",
    "G315",
    "G316",
    "G317",
    "G318",
    "G319",
    "G320",
    "G323",
    "G324",
    "G325",
    "G327",
    "G328",
    "G330",
    "G331",
    "G333",
    "G334",
    "G335",
    "G336",
    "G337",
    "G338",
    "G339",
    "G340",
    "G342",
    "G344",
    "G346",
    "G347",
    "G348",
    "G349",
    "G350",
    "G352",
    "G353",
    "G354",
    "G358",
    "G359",
    "G361",
    "G362",
    "G364",
    "G365",
    "G367",
    "G368",
    "G370",
    "G371",
    "G372",
    "G374",
    "G376",
    "G377",
    "G378",
    "G379",
    "G381",
    "G382",
    "G383",
    "G384",
    "G385",
    "G386",
    "G387",
    "G388",
    "G389",
    "G391",
    "G392",
    "G393",
    "G394",
    "G395",
    "G397",
    "G398",
    "G402",
    "G403",
    "G404",
    "G406",
    "G407",
    "G408",
    "G410",
    "G411",
    "G412",
    "G415",
    "G416",
    "G417",
    "G418",
    "G419",
    "G420",
    "G421",
    "G424",
    "G426",
    "G428",
    "G429",
    "G430",
    "G431",
    "G432",
    "G433",
    "G434",
    "G435",
    "G436",
    "G437",
    "G438",
    "G439",
    "G441",
    "G442",
    "G443",
    "G444",
    "G445",
    "G446",
    "G447",
    "G449",
    "G450",
    "G452",
    "G453",
    "G454",
    "G455",
    "G456",
    "G457",
    "G458",
    "G459",
    "G461",
    "G462",
    "G463",
    "G465",
    "G467",
    "G469",
    "G471",
    "G474",
    "G475",
    "G476",
    "G477",
    "G478",
    "G479",
    "G480",
    "G481",
    "G482",
    "G483",
    "G484",
    "G485",
    "G486",
    "G487",
    "G488",
    "G489",
    "G491",
    "G492",
    "G493",
    "G494",
    "G495",
    "G496",
    "G497",
    "G498",
    "G499",
    "G500",
    "G502",
    "G504",
    "G505",
    "G506",
    "G507",
    "G508",
    "G509",
    "G510",
    "G511",
    "G512",
    "G513",
    "G514",
    "G515",
    "G516",
    "G517",
    "G518",
    "G519",
    "G520",
    "G521",
    "G522",
    "G523",
    "G524",
    "G525",
    "G526",
    "G528",
    "G529",
    "G532",
    "G534",
    "G535",
    "G538",
    "G540",
    "G541",
    "G542",
    "G543",
    "G545",
    "G546",
    "G547",
    "G549",
    "G551",
    "G553",
    "G555",
    "G556",
    "G557",
    "G558",
    "G559",
    "G560",
    "G561",
    "G564",
    "G565",
    "G566",
    "G568",
    "G570",
    "G571",
    "G572",
    "G574",
    "G575",
    "G576",
    "G577",
    "G579",
    "G580",
    "G582",
    "G583",
    "G587",
    "G588",
    "G589",
    "G590",
    "G591",
    "G592",
    "G593",
    "G594",
    "G596",
    "G597",
    "G598",
    "G600",
    "G601",
    "G602",
    "G603",
    "G604",
    "G605",
    "G606",
    "G607",
    "G608",
    "G609",
    "G610",
    "G611",
    "G612",
    "G613",
    "G614",
    "G615",
    "G616",
    "G618",
    "G619",
    "G620",
    "G621",
    "G622",
    "G623",
    "G624",
    "G625",
    "G626",
    "G627",
    "G628",
    "G629",
    "G630",
    "G631",
    "G632",
    "G634",
    "G635",
    "G636",
    "G639",
    "G642",
    "G643",
    "G645",
    "G646",
    "G647",
    "G648",
    "G649",
    "G650",
    "G651",
    "G653",
    "G656",
    "G657",
    "G658",
    "G659",
    "G660",
    "G661",
    "G662",
    "G663",
    "G664",
    "G665",
    "G666",
    "G669",
    "G670",
    "G671",
    "G672",
    "G673",
    "G674",
    "G676",
    "G678",
    "G680",
    "G681",
    "G682",
    "G683",
    "G684",
    "G685",
    "G686",
    "G687",
    "G688",
    "G690",
    "G691",
    "G692",
    "G693",
    "G694",
    "G696",
    "G697",
    "G698",
    "G699",
    "G702",
    "G703",
    "G704",
    "G705",
    "G707",
    "G710",
    "G712",
    "G713",
    "G716",
    "G717",
    "G718",
    "G719",
    "G720",
    "G721",
    "G722",
    "G724",
    "G726",
    "G727",
    "G728",
    "G729",
    "G733",
    "G734",
    "G735",
    "G736",
    "G737",
    "G740",
    "G741",
    "G742",
    "G743",
    "G746",
    "G747",
    "G749",
    "G751",
    "G752",
    "G753",
    "G754",
    "G756",
    "G757",
    "G758",
    "G760",
    "G761",
    "G762",
    "G763",
    "G764",
    "G765",
    "G766",
    "G767",
    "G768",
    "G769",
    "G770",
    "G771",
    "G772",
    "G773",
    "G774",
    "G775",
    "G776",
    "G777",
    "G779",
    "G780",
    "G782",
    "G784",
    "G785",
    "G786",
    "G787",
    "G788",
    "G789",
    "G790",
    "G791",
    "G792",
    "G793",
    "G794",
    "G795",
    "G796",
    "G797",
    "G798",
    "G799",
    "G800",
    "G801",
    "G802",
    "G803",
    "G804",
    "G805",
    "G806",
    "G807",
    "G808",
    "G809",
    "G811",
    "G812",
    "G813",
    "G814",
    "G815",
    "G816",
    "G817",
    "G818",
    "G820",
    "G821",
    "G822",
    "G823",
    "G825",
    "G826",
    "G827",
    "G829",
    "G830",
    "G831",
    "G833",
    "G834",
    "G836",
    "G837",
    "G838",
    "G839",
    "G840",
    "G842",
    "G843",
    "G844",
    "G846",
    "G847",
    "G848",
    "G849",
    "G850",
    "G851",
    "G852",
    "G853",
    "G854",
    "G855",
    "G856",
    "G858",
    "G859",
    "G860",
    "G861",
    "G862",
    "G864",
    "G865",
    "G866",
    "G867",
    "G869",
    "G870",
    "G871",
    "G872",
    "G873",
    "G874",
    "G875",
    "G877",
    "G878",
    "G879",
    "G881",
    "G882",
    "G886",
    "G887",
    "G888",
    "G889",
    "G890",
    "G891",
    "G894",
    "G895",
    "G900",
    "G902",
    "G903",
    "G904",
    "G905",
    "G906",
    "G907",
    "G909",
    "G910",
    "G912",
    "G913",
    "G914",
    "G916",
    "G917",
    "G919",
    "G920",
    "G921",
    "G922",
    "G923",
    "G924",
    "G925",
    "G926",
    "G927",
    "G929",
    "G931",
    "G932",
    "G933",
    "G934",
    "G935",
    "G936",
    "G937",
    "G939",
    "G940",
    "G942",
    "G943",
    "G944",
    "G945",
    "G947",
    "G949",
    "G951",
    "G953",
    "G954",
    "G955",
    "G957",
    "G959",
    "G960",
    "G961",
    "G962",
    "G963",
    "G964",
    "G965",
    "G966",
    "G968",
    "G969",
    "G970",
    "G972",
    "G973",
    "G974",
    "G975",
    "G976",
    "G977",
    "G978",
    "G979",
    "G980",
    "G981",
    "G982",
    "G985",
    "G986",
    "G987",
    "G988",
    "G990",
    "G991",
    "G992",
    "G993",
    "G994",
    "G995",
    "G997",
    "G999",
    "H001",
    "H002",
    "H004",
    "H006",
    "H007",
    "H010",
    "H011",
    "H013",
    "H014",
    "H015",
    "H017",
    "H018",
    "H019",
    "H020",
    "H021",
    "H022",
    "H026",
    "H027",
    "H028",
    "H029",
    "H030",
    "H033",
    "H034",
    "H036",
    "H037",
    "H038",
    "H040",
    "H042",
    "H043",
    "H045",
    "H046",
    "H048",
    "H052",
    "H055",
    "H056",
    "H059",
    "H061",
    "H062",
    "H063",
    "H068",
    "H069",
    "H070",
    "H071",
    "H072",
    "H073",
    "H074",
    "H076",
    "H077",
    "H078",
    "H081",
    "H083",
    "H085",
    "H086",
    "H087",
    "H088",
    "H089",
    "H090",
    "H091",
    "H094",
    "H095",
    "H096",
    "H097",
    "H098",
    "H100",
    "H101",
    "H102",
    "H104",
    "H106",
    "H107",
    "H108",
    "H109",
    "H110",
    "H114",
    "H117",
    "H118",
    "H119",
    "H120",
    "H121",
    "H122",
    "H126",
    "H127",
    "H128",
    "H129",
    "H130",
    "H131",
    "H132",
    "H134",
    "H140",
    "H143",
    "H146",
    "H147",
    "H148",
    "H150",
    "H151",
    "H152",
    "H153",
    "H154",
    "H156",
    "H157",
    "H159",
    "H161",
    "H163",
    "H165",
    "H166",
    "H168",
    "H173",
    "H174",
    "H175",
    "H176",
    "H177",
    "H180",
    "H182",
    "H183",
    "H184",
    "H185",
    "H186",
    "H187",
    "H188",
    "H189",
    "H192",
    "H194",
    "H195",
    "H196",
    "H198",
    "H199",
    "H200",
    "H202",
    "H203",
    "H204",
    "H205",
    "H206",
    "H207",
    "H210",
    "H211",
    "H212",
    "H213",
    "H214",
    "H216",
    "H218",
    "H219",
    "H220",
    "H221",
    "H222",
    "H223",
    "H224",
    "H225",
    "H227",
    "H228",
    "H229",
    "H230",
    "H233",
    "H235",
    "H236",
    "H238",
    "H240",
    "H242",
    "H243",
    "H244",
    "H245",
    "H246",
    "H247",
    "H250",
    "H253",
    "H255",
    "H256",
    "H257",
    "H258",
    "H259",
    "H262",
    "H263",
    "H264",
    "H265",
    "H266",
    "H267",
    "H268",
    "H269",
    "H270",
    "H271",
    "H272",
    "H273",
    "H274",
    "H275",
    "H276",
    "H277",
    "H280",
    "H281",
    "H282",
    "H284",
    "H285",
    "H286",
    "H287",
    "H288",
    "H289",
    "H293",
    "H294",
    "H298",
    "H299",
    "H300",
    "H301",
    "H302",
    "H303",
    "H304",
    "H307",
    "H308",
    "H311",
    "H312",
    "H313",
    "H314",
    "H315",
    "H316",
    "H319",
    "H320",
    "H321",
    "H323",
    "H324",
    "H325",
    "H326",
    "H327",
    "H328",
    "H330",
    "H331",
    "H333",
    "H334",
    "H335",
    "H336",
    "H337",
    "H338",
    "H340",
    "H341",
    "H342",
    "H343",
    "H344",
    "H346",
    "H347",
    "H348",
    "H350",
    "H353",
    "H354",
    "H355",
    "H356",
    "H357",
    "H359",
    "H361",
    "H362",
    "H363",
    "H364",
    "H365",
    "H366",
    "H367",
    "H369",
    "H371",
    "H372",
    "H373",
    "H375",
    "H376",
    "H377",
    "H378",
    "H379",
    "H380",
    "H382",
    "H383",
    "H384",
    "H385",
    "H386",
    "H387",
    "H389",
    "H390",
    "H391",
    "H392",
    "H393",
    "H394",
    "H395",
    "H396",
    "H398",
    "H399",
    "H400",
    "H401",
    "H402",
    "H403",
    "H404",
    "H405",
    "H406",
    "H407",
    "H408",
    "H409",
    "H410",
    "H411",
    "H412",
    "H413",
    "H414",
    "H416",
    "H417",
    "H418",
    "H420",
    "H421",
    "H422",
    "H423",
    "H424",
    "H425",
    "H426",
    "H427",
    "H428",
    "H429",
    "H431",
    "H432",
    "H433",
    "H434",
    "H436",
    "H437",
    "H438",
    "H439",
    "H440",
    "H441",
    "H442",
    "H443",
    "H444",
    "H445",
    "H446",
    "H447",
    "H448",
    "H449",
    "H450",
    "H451",
    "H452",
    "H453",
    "H454",
    "H455",
    "H456",
    "H458",
    "H459",
    "H460",
    "H461",
    "H462",
    "H465",
    "H466",
    "H467",
    "H468",
    "H470",
    "H472",
    "H473",
    "H474",
    "H475",
    "H477",
    "H478",
    "H479",
    "H480",
    "H481",
    "H484",
    "H485",
    "H486",
    "H488",
    "H489",
    "H490",
    "H491",
    "H492",
    "H493",
    "H494",
    "H495",
    "H497",
    "H498",
    "H500",
    "H501",
    "H502",
    "H503",
    "H505",
    "H507",
    "H508",
    "H509",
    "H511",
    "H512",
    "H514",
    "H516",
    "H517",
    "H518",
    "H519",
    "H521",
    "H522",
    "H523",
    "H525",
    "H527",
    "H528",
    "H529",
    "H531",
    "H532",
    "H533",
    "H534",
    "H535",
    "H536",
    "H537",
    "H538",
    "H539",
    "H540",
    "H541",
    "H542",
    "H544",
    "H546",
    "H547",
    "H549",
    "H552",
    "H553",
    "H554",
    "H555",
    "H556",
    "H558",
    "H559",
    "H560",
    "H561",
    "H562",
    "H564",
    "H565",
    "H566",
    "H568",
    "H569",
    "H570",
    "H572",
    "H573",
    "H574",
    "H575",
    "H577",
    "H578",
    "H580",
    "H581",
    "H583",
    "H584",
    "H585",
    "H588",
    "H589",
    "H590",
    "H591",
    "H592",
    "H593",
    "H594",
    "H598",
    "H599",
    "H601",
    "H602",
    "H604",
    "H606",
    "H607",
    "H608",
    "H609",
    "H610",
    "H612",
    "H614",
    "H615",
    "H618",
    "H620",
    "H621",
    "H622",
    "H623",
    "H625",
    "H627",
    "H628",
    "H629",
    "H630",
    "H631",
    "H632",
    "H633",
    "H634",
    "H635",
    "H639",
    "H641",
    "H642",
    "H643",
    "H644",
    "H645",
    "H646",
    "H647",
    "H650",
    "H652",
    "H654",
    "H655",
    "H657",
    "H658",
    "H659",
    "H661",
    "H662",
    "H665",
    "H666",
    "H669",
    "H670",
    "H671",
    "H672",
    "H673",
    "H674",
    "H675",
    "H676",
    "H677",
    "H678",
    "H679",
    "H681",
    "H682",
    "H683",
    "H684",
    "H686",
    "H687",
    "H688",
    "H689",
    "H690",
    "H691",
    "H693",
    "H694",
    "H695",
    "H699",
    "H700",
    "H701",
    "H702",
    "H703",
    "H704",
    "H706",
    "H707",
    "H708",
    "H710",
    "H712",
    "H713",
    "H714",
    "H715",
    "H716",
    "H717",
    "H719",
    "H720",
    "H723",
    "H724",
    "H725",
    "H726",
    "H727",
    "H729",
    "H730",
    "H731",
    "H732",
    "H733",
    "H734",
    "H735",
    "H736",
    "H738",
    "H739",
    "H743",
    "H744",
    "H745",
    "H746",
    "H749",
    "H752",
    "H753",
    "H754",
    "H755",
    "H756",
    "H757",
    "H760",
    "H763",
    "H764",
    "H765",
    "H766",
    "H767",
    "H768",
    "H769",
    "H770",
    "H771",
    "H772",
    "H773",
    "H774",
    "H775",
    "H777",
    "H778",
    "H779",
    "H780",
    "H781",
    "H782",
    "H783",
    "H784",
    "H785",
    "H786",
    "H787",
    "H789",
    "H790",
    "H791",
    "H792",
    "H793",
    "H794",
    "H795",
    "H796",
    "H797",
    "H798",
    "H799",
    "H800",
    "H801",
    "H802",
    "H803",
    "H804",
    "H805",
    "H806",
    "H807",
    "H808",
    "H809",
    "H810",
    "H811",
    "H812",
    "H814",
    "H815",
    "H816",
    "H818",
    "H819",
    "H820",
    "H821",
    "H822",
    "H823",
    "H824",
    "H825",
    "H826",
    "H827",
    "H829",
    "H831",
    "H833",
    "H834",
    "H835",
    "H836",
    "H838",
    "H839",
    "H840",
    "H841",
    "H842",
    "H843",
    "H844",
    "H845",
    "H846",
    "H847",
    "H848",
    "H850",
    "H851",
    "H852",
    "H855",
    "H856",
    "H857",
    "H858",
    "H859",
    "H860",
    "H861",
    "H862",
    "H865",
    "H867",
    "H868",
    "H870",
    "H872",
    "H873",
    "H875",
    "H876",
    "H877",
    "H878",
    "H880",
    "H881",
    "H882",
    "H883",
    "H884",
    "H885",
    "H887",
    "H888",
    "H889",
    "H890",
    "H891",
    "H892",
    "H893",
    "H894",
    "H895",
    "H896",
    "H897",
    "H898",
    "H899",
    "H900",
    "H901",
    "H903",
    "H906",
    "H907",
    "H910",
    "H912",
    "H913",
    "H914",
    "H916",
    "H917",
    "H918",
    "H919",
    "H920",
    "H921",
    "H922",
    "H923",
    "H924",
    "H926",
    "H928",
    "H929",
    "H930",
    "H931",
    "H933",
    "H935",
    "H936",
    "H937",
    "H938",
    "H939",
    "H940",
    "H941",
    "H942",
    "H943",
    "H944",
    "H945",
    "H949",
    "H951",
    "H952",
    "H953",
    "H955",
    "H956",
    "H957",
    "H958",
    "H959",
    "H961",
    "H962",
    "H964",
    "H967",
    "H969",
    "H970",
    "H971",
    "H973",
    "H974",
    "H975",
    "H976",
    "H977",
    "H978",
    "H979",
    "H981",
    "H982",
    "H984",
    "H985",
    "H986",
    "H987",
    "H988",
    "H989",
    "H990",
    "H991",
    "H992",
    "H994",
    "H996",
    "H997",
    "H999",
    "I002",
    "I003",
    "I005",
    "I007",
    "I008",
    "I011",
    "I012",
    "I014",
    "I016",
    "I017",
    "I018",
    "I019",
    "I023",
    "I024",
    "I025",
    "I026",
    "I027",
    "I028",
    "I029",
    "I030",
    "I031",
    "I032",
    "I034",
    "I035",
    "I037",
    "I040",
    "I042",
    "I045",
    "I046",
    "I048",
    "I049",
    "I051",
    "I052",
    "I053",
    "I054",
    "I056",
    "I057",
    "I058",
    "I059",
    "I060",
    "I061",
    "I062",
    "I063",
    "I065",
    "I066",
    "I071",
    "I072",
    "I073",
    "I076",
    "I079",
    "I082",
    "I084",
    "I086",
    "I088",
    "I089",
    "I090",
    "I092",
    "I093",
    "I095",
    "I096",
    "I098",
    "I102",
    "I103",
    "I105",
    "I107",
    "I108",
    "I109",
    "I110",
    "I113",
    "I114",
    "I115",
    "I116",
    "I117",
    "I118",
    "I119",
    "I120",
    "I121",
    "I122",
    "I123",
    "I124",
    "I125",
    "I126",
    "I128",
    "I129",
    "I130",
    "I131",
    "I132",
    "I133",
    "I135",
    "I136",
    "I137",
    "I138",
    "I139",
    "I140",
    "I142",
    "I143",
    "I144",
    "I145",
    "I147",
    "I148",
    "I150",
    "I151",
    "I152",
    "I153",
    "I154",
    "I155",
    "I156",
    "I157",
    "I158",
    "I162",
    "I163",
    "I164",
    "I165",
    "I166",
    "I168",
    "I169",
    "I170",
    "I171",
    "I172",
    "I173",
    "I174",
    "I175",
    "I176",
    "I177",
    "I178",
    "I179",
    "I181",
    "I182",
    "I183",
    "I184",
    "I185",
    "I187",
    "I188",
    "I189",
    "I190",
    "I191",
    "I192",
    "I193",
    "I196",
    "I197",
    "I198",
    "I199",
    "I201",
    "I202",
    "I203",
    "I205",
    "I206",
    "I207",
    "I208",
    "I210",
    "I213",
    "I214",
    "I215",
    "I216",
    "I217",
    "I219",
    "I220",
    "I221",
    "I224",
    "I225",
    "I230",
    "I232",
    "I233",
    "I234",
    "I236",
    "I237",
    "I238",
    "I240",
    "I242",
    "I243",
    "I244",
    "I247",
    "I248",
    "I249",
    "I251",
    "I253",
    "I254",
    "I255",
    "I256",
    "I258",
    "I259",
    "I260",
    "I261",
    "I262",
    "I263",
    "I264",
    "I265",
    "I266",
    "I271",
    "I273",
    "I274",
    "I275",
    "I276",
    "I277",
    "I278",
    "I279",
    "I280",
    "I281",
    "I282",
    "I283",
    "I284",
    "I286",
    "I287",
    "I288",
    "I289",
    "I290",
    "I291",
    "I292",
    "I293",
    "I294",
    "I296",
    "I298",
    "I300",
    "I301",
    "I302",
    "I304",
    "I305",
    "I306",
    "I307",
    "I308",
    "I309",
    "I310",
    "I311",
    "I312",
    "I314",
    "I315",
    "I316",
    "I317",
    "I318",
    "I319",
    "I320",
    "I321",
    "I322",
    "I324",
    "I326",
    "I327",
    "I328",
    "I329",
    "I330",
    "I332",
    "I333",
    "I335",
    "I336",
    "I337",
    "I339",
    "I341",
    "I342",
    "I344",
    "I346",
    "I347",
    "I348",
    "I350",
    "I351",
    "I352",
    "I353",
    "I354",
    "I356",
    "I357",
    "I359",
    "I360",
    "I361",
    "I362",
    "I363",
    "I364",
    "I365",
    "I367",
    "I368",
    "I370",
    "I371",
    "I372",
    "I373",
    "I374",
    "I375",
    "I376",
    "I377",
    "I381",
    "I382",
    "I384",
    "I388",
    "I389",
    "I390",
    "I391",
    "I392",
    "I393",
    "I394",
    "I396",
    "I400",
    "I401",
    "I402",
    "I403",
    "I404",
    "I405",
    "I407",
    "I408",
    "I409",
    "I410",
    "I411",
    "I412",
    "I414",
    "I415",
    "I416",
    "I417",
    "I418",
    "I420",
    "I421",
    "I422",
    "I423",
    "I424",
    "I425",
    "I426",
    "I428",
    "I429",
    "I430",
    "I431",
    "I432",
    "I433",
    "I434",
    "I435",
    "I436",
    "I437",
    "I438",
    "I439",
    "I441",
    "I442",
    "I443",
    "I444",
    "I445",
    "I447",
    "I448",
    "I449",
    "I451",
    "I452",
    "I453",
    "I454",
    "I455",
    "I457",
    "I460",
    "I461",
    "I462",
    "I463",
    "I464",
    "I465",
    "I466",
    "I467",
    "I468",
    "I469",
    "I470",
    "I471",
    "I472",
    "I473",
    "I475",
    "I476",
    "I477",
    "I478",
    "I479",
    "I480",
    "I482",
    "I483",
    "I484",
    "I485",
    "I486",
    "I487",
    "I489",
    "I490",
    "I492",
    "I493",
    "I494",
    "I496",
    "I497",
    "I498",
    "I499",
    "I501",
    "I503",
    "I504",
    "I506",
    "I507",
    "I510",
    "I511",
    "I512",
    "I519",
    "I520",
    "I522",
    "I523",
    "I526",
    "I527",
    "I529",
    "I530",
    "I531",
    "I532",
    "I533",
    "I534",
    "I535",
    "I536",
    "I537",
    "I538",
    "I539",
    "I540",
    "I541",
    "I543",
    "I544",
    "I545",
    "I546",
    "I548",
    "I549",
    "I551",
    "I553",
    "I554",
    "I555",
    "I556",
    "I558",
    "I559",
    "I561",
    "I562",
    "I563",
    "I564",
    "I565",
    "I566",
    "I567",
    "I569",
    "I570",
    "I571",
    "I573",
    "I576",
    "I577",
    "I578",
    "I580",
    "I581",
    "I582",
    "I585",
    "I588",
    "I589",
    "I590",
    "I591",
    "I592",
    "I593",
    "I594",
    "I595",
    "I597",
    "I598",
    "I599",
    "I600",
    "I601",
    "I602",
    "I603",
    "I604",
    "I605",
    "I606",
    "I607",
    "I608",
    "I609",
    "I610",
    "I611",
    "I612",
    "I613",
    "I614",
    "I615",
    "I618",
    "I621",
    "I622",
    "I624",
    "I625",
    "I626",
    "I627",
    "I628",
    "I629",
    "I630",
    "I631",
    "I632",
    "I633",
    "I634",
    "I635",
    "I636",
    "I637",
    "I639",
    "I640",
    "I641",
    "I642",
    "I643",
    "I644",
    "I645",
    "I646",
    "I647",
    "I648",
    "I649",
    "I651",
    "I652",
    "I653",
    "I654",
    "I655",
    "I656",
    "I657",
    "I659",
    "I660",
    "I661",
    "I662",
    "I663",
    "I666",
    "I667",
    "I668",
    "I669",
    "I671",
    "I673",
    "I676",
    "I677",
    "I678",
    "I679",
    "I681",
    "I682",
    "I683",
    "I684",
    "I686",
    "I687",
    "I688",
    "I689",
    "I690",
    "I692",
    "I693",
    "I695",
    "I696",
    "I697",
    "I698",
    "I699",
    "I700",
    "I701",
    "I702",
    "I703",
    "I704",
    "I705",
    "I706",
    "I707",
    "I709",
    "I711",
    "I712",
    "I714",
    "I715",
    "I716",
    "I717",
    "I718",
    "I720",
    "I721",
    "I723",
    "I724",
    "I725",
    "I726",
    "I727",
    "I728",
    "I729",
    "I730",
    "I732",
    "I734",
    "I735",
    "I736",
    "I738",
    "I739",
    "I741",
    "I742",
    "I743",
    "I744",
    "I745",
    "I747",
    "I748",
    "I749",
    "I750",
    "I751",
    "I752",
    "I753",
    "I754",
    "I756",
    "I757",
    "I758",
    "I759",
    "I761",
    "I765",
    "I767",
    "I771",
    "I774",
    "I775",
    "I777",
    "I778",
    "I779",
    "I780",
    "I781",
    "I782",
    "I783",
    "I785",
    "I786",
    "I787",
    "I790",
    "I791",
    "I793",
    "I794",
    "I796",
    "I797",
    "I798",
    "I799",
    "I800",
    "I801",
    "I802",
    "I803",
    "I804",
    "I805",
    "I808",
    "I809",
    "I812",
    "I813",
    "I815",
    "I817",
    "I819",
    "I820",
    "I821",
    "I822",
    "I823",
    "I824",
    "I825",
    "I826",
    "I827",
    "I828",
    "I829",
    "I830",
    "I831",
    "I832",
    "I838",
    "I839",
    "I840",
    "I841",
    "I843",
    "I844",
    "I847",
    "I848",
    "I849",
    "I850",
    "I851",
    "I852",
    "I853",
    "I854",
    "I855",
    "I856",
    "I857",
    "I858",
    "I860",
    "I861",
    "I862",
    "I863",
    "I864",
    "I865",
    "I866",
    "I867",
    "I868",
    "I869",
    "I871",
    "I872",
    "I873",
    "I874",
    "I875",
    "I876",
    "I877",
    "I878",
    "I879",
    "I880",
    "I881",
    "I884",
    "I885",
    "I886",
    "I887",
    "I888",
    "I891",
    "I892",
    "I893",
    "I894",
    "I895",
    "I896",
    "I899",
    "I901",
    "I902",
    "I903",
    "I904",
    "I905",
    "I906",
    "I907",
    "I908",
    "I909",
    "I910",
    "I911",
    "I912",
    "I914",
    "I916",
    "I917",
    "I919",
    "I921",
    "I922",
    "I923",
    "I924",
    "I925",
    "I926",
    "I927",
    "I928",
    "I929",
    "I930",
    "I932",
    "I935",
    "I936",
    "I937",
    "I938",
    "I939",
    "I941",
    "I942",
    "I943",
    "I945",
    "I946",
    "I947",
    "I948",
    "I949",
    "I950",
    "I951",
    "I953",
    "I954",
    "I955",
    "I956",
    "I959",
    "I960",
    "I962",
    "I963",
    "I964",
    "I965",
    "I968",
    "I969",
    "I970",
    "I973",
    "I974",
    "I975",
    "I976",
    "I977",
    "I978",
    "I980",
    "I981",
    "I982",
    "I984",
    "I985",
    "I986",
    "I990",
    "I991",
    "I992",
    "I993",
    "I994",
    "I995",
    "I996",
    "I997",
    "I998",
    "L002",
    "L003",
    "L004",
    "L006",
    "L007",
    "L008",
    "L009",
    "L010",
    "L011",
    "L013",
    "L014",
    "L015",
    "L016",
    "L017",
    "L018",
    "L019",
    "L020",
    "L022",
    "L023",
    "L024",
    "L025",
    "L026",
    "L027",
    "L030",
    "L032",
    "L034",
    "L035",
    "L036",
    "L037",
    "L038",
    "L039",
    "L040",
    "L042",
    "L046",
    "L047",
    "L048",
    "L049",
    "L050",
    "L055",
    "L056",
    "L057",
    "L058",
    "L059",
    "L061",
    "L062",
    "L063",
    "L064",
    "L065",
    "L066",
    "L069",
    "L070",
    "L071",
    "L073",
    "L074",
    "L075",
    "L078",
    "L081",
    "L082",
    "L083",
    "L084",
    "L085",
    "L086",
    "L087",
    "L088",
    "L089",
    "L090",
    "L093",
    "L094",
    "L096",
    "L097",
    "L100",
    "L102",
    "L103",
    "L104",
    "L105",
    "L106",
    "L108",
    "L109",
    "L111",
    "L112",
    "L113",
    "L115",
    "L116",
    "L117",
    "L118",
    "L120",
    "L121",
    "L122",
    "L123",
    "L124",
    "L125",
    "L126",
    "L127",
    "L131",
    "L132",
    "L134",
    "L136",
    "L138",
    "L139",
    "L140",
    "L142",
    "L143",
    "L144",
    "L145",
    "L146",
    "L147",
    "L149",
    "L150",
    "L152",
    "L153",
    "L154",
    "L155",
    "L156",
    "L157",
    "L158",
    "L160",
    "L164",
    "L165",
    "L166",
    "L167",
    "L168",
    "L169",
    "L172",
    "L173",
    "L174",
    "L175",
    "L176",
    "L177",
    "L178",
    "L180",
    "L181",
    "L182",
    "L183",
    "L184",
    "L185",
    "L186",
    "L187",
    "L188",
    "L189",
    "L190",
    "L191",
    "L192",
    "L193",
    "L194",
    "L195",
    "L197",
    "L199",
    "L200",
    "L202",
    "L203",
    "L204",
    "L205",
    "L206",
    "L207",
    "L210",
    "L211",
    "L212",
    "L213",
    "L214",
    "L215",
    "L216",
    "L217",
    "L218",
    "L219",
    "L220",
    "L221",
    "L223",
    "L224",
    "L225",
    "L227",
    "L228",
    "L229",
    "L230",
    "L231",
    "L233",
    "L235",
    "L237",
    "L238",
    "L239",
    "L240",
    "L241",
    "L243",
    "L244",
    "L245",
    "L246",
    "L247",
    "L248",
    "L250",
    "L251",
    "L252",
    "L253",
    "L254",
    "L256",
    "L257",
    "L258",
    "L259",
    "L262",
    "L263",
    "L265",
    "L267",
    "L269",
    "L270",
    "L271",
    "L272",
    "L273",
    "L274",
    "L276",
    "L277",
    "L278",
    "L279",
    "L280",
    "L281",
    "L282",
    "L284",
    "L285",
    "L286",
    "L287",
    "L290",
    "L291",
    "L292",
    "L293",
    "L294",
    "L295",
    "L296",
    "L297",
    "L298",
    "L299",
    "L301",
    "L302",
    "L303",
    "L304",
    "L305",
    "L306",
    "L307",
    "L308",
    "L309",
    "L310",
    "L312",
    "L314",
    "L315",
    "L316",
    "L317",
    "L319",
    "L321",
    "L322",
    "L323",
    "L324",
    "L325",
    "L326",
    "L327",
    "L328",
    "L330",
    "L331",
    "L332",
    "L333",
    "L334",
    "L335",
    "L336",
    "L337",
    "L339",
    "L340",
    "L342",
    "L345",
    "L346",
    "L347",
    "L348",
    "L349",
    "L353",
    "L355",
    "L356",
    "L357",
    "L359",
    "L361",
    "L363",
    "L364",
    "L366",
    "L367",
    "L369",
    "L372",
    "L377",
    "L378",
    "L379",
    "L380",
    "L382",
    "L383",
    "L384",
    "L386",
    "L388",
    "L389",
    "L392",
    "L393",
    "L396",
    "L397",
    "L398",
    "L399",
    "L400",
    "L401",
    "L402",
    "L403",
    "L404",
    "L406",
    "L407",
    "L408",
    "L409",
    "L410",
    "L411",
    "L413",
    "L414",
    "L415",
    "L416",
    "L418",
    "L419",
    "L420",
    "L421",
    "L423",
    "L424",
    "L425",
    "L426",
    "L427",
    "L428",
    "L429",
    "L430",
    "L431",
    "L432",
    "L433",
    "L434",
    "L435",
    "L437",
    "L438",
    "L439",
    "L440",
    "L444",
    "L445",
    "L447",
    "L448",
    "L449",
    "L450",
    "L451",
    "L452",
    "L453",
    "L454",
    "L455",
    "L458",
    "L459",
    "L460",
    "L461",
    "L462",
    "L463",
    "L464",
    "L466",
    "L468",
    "L469",
    "L470",
    "L471",
    "L472",
    "L473",
    "L474",
    "L475",
    "L477",
    "L478",
    "L480",
    "L482",
    "L483",
    "L484",
    "L485",
    "L487",
    "L488",
    "L489",
    "L490",
    "L492",
    "L494",
    "L496",
    "L497",
    "L498",
    "L499",
    "L500",
    "L501",
    "L502",
    "L503",
    "L505",
    "L506",
    "L507",
    "L508",
    "L509",
    "L511",
    "L512",
    "L513",
    "L514",
    "L515",
    "L516",
    "L517",
    "L519",
    "L521",
    "L522",
    "L524",
    "L525",
    "L526",
    "L527",
    "L528",
    "L529",
    "L531",
    "L532",
    "L533",
    "L535",
    "L537",
    "L538",
    "L539",
    "L540",
    "L544",
    "L545",
    "L546",
    "L547",
    "L551",
    "L552",
    "L554",
    "L555",
    "L556",
    "L557",
    "L558",
    "L561",
    "L562",
    "L563",
    "L564",
    "L565",
    "L566",
    "L567",
    "L568",
    "L569",
    "L570",
    "L571",
    "L572",
    "L573",
    "L574",
    "L575",
    "L576",
    "L577",
    "L578",
    "L579",
    "L580",
    "L581",
    "L582",
    "L583",
    "L584",
    "L586",
    "L588",
    "L589",
    "L590",
    "L591",
    "L593",
    "L594",
    "L595",
    "L596",
    "L597",
    "L598",
    "L599",
    "L601",
    "L603",
    "L604",
    "L605",
    "L607",
    "L609",
    "L611",
    "L612",
    "L613",
    "L614",
    "L616",
    "L617",
    "L620",
    "L623",
    "L624",
    "L625",
    "L626",
    "L627",
    "L628",
    "L629",
    "L631",
    "L633",
    "L634",
    "L638",
    "L639",
    "L640",
    "L641",
    "L642",
    "L643",
    "L644",
    "L647",
    "L651",
    "L653",
    "L654",
    "L655",
    "L656",
    "L658",
    "L660",
    "L664",
    "L665",
    "L666",
    "L667",
    "L668",
    "L669",
    "L670",
    "L671",
    "L672",
    "L673",
    "L675",
    "L676",
    "L677",
    "L680",
    "L681",
    "L682",
    "L685",
    "L686",
    "L687",
    "L689",
    "L690",
    "L691",
    "L693",
    "L696",
    "L698",
    "L699",
    "L700",
    "L702",
    "L703",
    "L704",
    "L706",
    "L707",
    "L709",
    "L710",
    "L711",
    "L712",
    "L713",
    "L715",
    "L716",
    "L719",
    "L720",
    "L722",
    "L723",
    "L724",
    "L725",
    "L726",
    "L727",
    "L728",
    "L729",
    "L730",
    "L733",
    "L734",
    "L735",
    "L736",
    "L737",
    "L738",
    "L739",
    "L740",
    "L741",
    "L742",
    "L743",
    "L744",
    "L745",
    "L746",
    "L747",
    "L748",
    "L749",
    "L750",
    "L751",
    "L752",
    "L753",
    "L758",
    "L762",
    "L764",
    "L765",
    "L769",
    "L771",
    "L772",
    "L773",
    "L774",
    "L775",
    "L776",
    "L777",
    "L778",
    "L779",
    "L780",
    "L781",
    "L783",
    "L784",
    "L785",
    "L787",
    "L788",
    "L792",
    "L795",
    "L797",
    "L799",
    "L801",
    "L802",
    "L804",
    "L805",
    "L806",
    "L807",
    "L808",
    "L809",
    "L810",
    "L811",
    "L812",
    "L814",
    "L815",
    "L816",
    "L817",
    "L819",
    "L820",
    "L823",
    "L826",
    "L827",
    "L828",
    "L829",
    "L830",
    "L831",
    "L833",
    "L834",
    "L835",
    "L836",
    "L837",
    "L838",
    "L840",
    "L841",
    "L842",
    "L843",
    "L844",
    "L845",
    "L846",
    "L847",
    "L848",
    "L850",
    "L851",
    "L854",
    "L856",
    "L857",
    "L858",
    "L859",
    "L860",
    "L865",
    "L866",
    "L868",
    "L869",
    "L872",
    "L873",
    "L874",
    "L876",
    "L878",
    "L879",
    "L880",
    "L881",
    "L882",
    "L883",
    "L885",
    "L886",
    "L887",
    "L888",
    "L889",
    "L890",
    "L892",
    "L894",
    "L897",
    "L898",
    "L899",
    "L900",
    "L904",
    "L905",
    "L906",
    "L907",
    "L908",
    "L909",
    "L912",
    "L913",
    "L915",
    "L916",
    "L917",
    "L919",
    "L920",
    "L922",
    "L923",
    "L924",
    "L926",
    "L928",
    "L929",
    "L931",
    "L933",
    "L934",
    "L936",
    "L937",
    "L938",
    "L939",
    "L942",
    "L943",
    "L944",
    "L945",
    "L946",
    "L947",
    "L948",
    "L949",
    "L950",
    "L951",
    "L952",
    "L953",
    "L956",
    "L957",
    "L958",
    "L959",
    "L961",
    "L963",
    "L964",
    "L965",
    "L966",
    "L967",
    "L968",
    "L969",
    "L970",
    "L971",
    "L972",
    "L973",
    "L974",
    "L975",
    "L977",
    "L978",
    "L979",
    "L980",
    "L981",
    "L982",
    "L983",
    "L984",
    "L985",
    "L986",
    "L987",
    "L988",
    "L989",
    "L990",
    "L991",
    "L992",
    "L994",
    "L995",
    "L998",
    "L999",
    "M002",
    "M003",
    "M004",
    "M007",
    "M009",
    "M011",
    "M013",
    "M014",
    "M015",
    "M016",
    "M017",
    "M018",
    "M019",
    "M021",
    "M022",
    "M023",
    "M025",
    "M026",
    "M027",
    "M028",
    "M030",
    "M031",
    "M032",
    "M041",
    "M042",
    "M043",
    "M044",
    "M045",
    "M048",
    "M050",
    "M052",
    "M053",
    "M055",
    "M057",
    "M058",
    "M059",
    "M060",
    "M062",
    "M063",
    "M065",
    "M067",
    "M069",
    "M070",
    "M071",
    "M072",
    "M073",
    "M077",
    "M078",
    "M079",
    "M080",
    "M081",
    "M082",
    "M083",
    "M085",
    "M086",
    "M088",
    "M089",
    "M090",
    "M091",
    "M092",
    "M093",
    "M094",
    "M095",
    "M096",
    "M098",
    "M100",
    "M101",
    "M102",
    "M103",
    "M104",
    "M105",
    "M106",
    "M108",
    "M109",
    "M110",
    "M111",
    "M113",
    "M115",
    "M116",
    "M118",
    "M119",
    "M120",
    "M121",
    "M122",
    "M123",
    "M124",
    "M125",
    "M126",
    "M127",
    "M130",
    "M131",
    "M132",
    "M133",
    "M136",
    "M138",
    "M139",
    "M140",
    "M141",
    "M143",
    "M144",
    "M145",
    "M147",
    "M150",
    "M152",
    "M153",
    "M156",
    "M158",
    "M161",
    "M162",
    "M163",
    "M165",
    "M166",
    "M167",
    "M168",
    "M169",
    "M170",
    "M171",
    "M172",
    "M173",
    "M176",
    "M177",
    "M178",
    "M179",
    "M180",
    "M182",
    "M183",
    "M184",
    "M185",
    "M187",
    "M188",
    "M189",
    "M190",
    "M194",
    "M196",
    "M197",
    "M199",
    "M200",
    "M201",
    "M202",
    "M203",
    "M204",
    "M207",
    "M208",
    "M209",
    "M210",
    "M211",
    "M212",
    "M213",
    "M214",
    "M253",
    "M255",
    "M256",
    "M257",
    "M258",
    "M259",
    "M260",
    "M261",
    "M262",
    "M263",
    "M264",
    "M265",
    "M266",
    "M267",
    "M268",
    "M269",
    "M270",
    "M271",
    "M272",
    "M273",
    "M274",
    "M275",
    "M276",
    "M277",
    "M278",
    "M279",
    "M280",
    "M281",
    "M282",
    "M283",
    "M284",
    "M285",
    "M286",
    "M287",
    "M288",
    "M289",
    "M290",
    "M291",
    "M292",
    "M294",
    "M295",
    "M297",
    "M298",
    "M300",
    "M301",
    "M302",
    "M303",
    "M308",
    "M309",
    "M311",
    "M312",
    "M313",
    "M314",
    "M315",
    "M316",
    "M317",
    "M318",
    "M319",
    "M320",
    "M321",
    "M322",
    "M323",
    "M324",
    "M325",
    "M326",
    "M327",
    "M328",
    "M329",
    "M330",
    "M331",
    "M332",
    "M333",
    "M334",
    "M335",
    "M336",
    "M337",
    "M338",
    "M339",
    "M340",
    "M341",
    "M342",
    "M343",
    "M344",
    "M345",
    "M346",
    "M347",
    "M348",
    "M349",
    "M350",
    "M351",
    "M352",
    "M353",
    "M354",
    "M355",
    "M356",
    "M357",
    "M358",
    "M359",
    "M360",
    "M361",
    "M362",
    "M363",
    "M364",
    "M365",
    "M366",
    "M367",
    "M368",
    "M369",
    "M370",
    "M371",
    "M372",
    "M373",
    "M374",
    "M375",
    "M376",
    "M377",
    "M378",
    "M379",
    "M380",
    "M381",
    "M382",
    "M383",
    "M384",
    "M385",
    "M386",
    "M387",
    "M388",
    "M389",
    "M390",
    "M391",
    "M392",
    "M393",
    "M394",
    "M395",
    "M396",
    "M397",
    "M398",
    "M399",
    "M400",
    "M401",
    "M402",
    "M403",
    "M404",
    "M405",
    "M406",
    "M407",
    "M408",
    "M409",
    "M410",
    "M411",
    "M412",
    "M413",
    "M414",
    "M415",
    "M416",
    "M417",
    "M418",
    "M419",
    "M420",
    "M421",
    "M422",
    "M423",
    "M424",
    "M425",
    "M426",
    "M427",
    "M428",
    "M429",
    "M430",
    "M431",
    "M432",
)
CHECKSUM_TABLE = (
    (
        1,
        0,
        5,
        7,
        9,
        13,
        15,
        17,
        19,
        21,
        1,
        0,
        5,
        7,
        9,
        13,
        15,
        17,
        19,
        21,
        2,
        4,
        18,
        20,
        11,
        3,
        6,
        8,
        12,
        14,
        16,
        10,
        22,
        25,
        24,
        23,
    ),
    (
        0,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        0,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
    ),
)


def checksum(value: str) -> str:
    """
    Calculates the checksum char used for the 16th char.
    Author: Vincenzo Palazzo
    """
    return chr(65 + sum(CHECKSUM_TABLE[index % 2][ALPHANUMERICS_DICT[char]] for index, char in enumerate(value)) % 26)


class Provider(SsnProvider):
    """
    Generates italian fiscal codes.
    """

    def ssn(self) -> str:
        sex: int = self.random_int(min=0, max=1)
        surname: str = self._get_surname_letters()
        name: str = self._get_name_letters(sex)
        year: str = "%02d" % self.random_int(min=0, max=99)
        is_leap_year: bool = self.is_leap_year(int(year))
        month: str = self.random_element(MONTHS_LIST)
        max_day: int = self._get_max_day(is_leap_year=is_leap_year, month=month)
        day: str = "%02d" % (self.random_int(min=1, max=max_day) + (40 if sex == 1 else 0))
        municipality: str = self.random_element(MUNICIPALITIES_LIST)
        code: str = f"{surname}{name}{year}{month}{day}{municipality}"
        return code + checksum(code)

    vat_id_formats = ("IT###########",)

    def vat_id(self) -> str:
        """
        http://ec.europa.eu/taxation_customs/vies/faq.html#item_11
        :return: A random Italian VAT ID
        """
        return self.bothify(self.random_element(self.vat_id_formats))

    def _get_name_letters(self, sex: int) -> str:
        """
        Rules:
            * take all consonants in their order
              * if >= 4, take the 1st, 3rd and 4th
              * if < 3 take the vowels also; vowels must go _after_ the consonants
                and must be taken in the order they appear (LUCA -> LCU)
              * if == 3 return all 3 consonants
            * if name is < 3 chars, pad it on the right with "X" (LI -> LIX)
        Args:
            sex: int
        Returns:
            str
        """

        if sex == 1:
            name = self.generator.first_name_male().upper()
        else:
            name = self.generator.first_name_female().upper()

        name = self._transliterate_name(name)

        if len(name) < 3:
            return self._pad_shorter(name)

        name_consonants = self._get_consonants(name)
        cons_len = len(name_consonants)
        if cons_len >= 4:
            name_part = "".join([name_consonants[0], name_consonants[1], name_consonants[3]])
        elif cons_len < 3:
            name_part = "".join(name_consonants + self._get_vowels(name))[:3]
        else:
            name_part = "".join(name_consonants)
        return name_part

    def _get_surname_letters(self) -> str:
        """
            Rules:
            * if consonants >=3 : take the first 3
            * if less, pad them with vowels; vowels come after the consonants and in the order they appear (ROSA -> RSO)
            * if surname is less than 3 chars, pad it on the right with 'X'  (FO -> FOX)
        Returns:
            str
        """
        surname = self.generator.last_name().upper()
        surname = self._transliterate_name(surname)

        if len(surname) < 3:
            return self._pad_shorter(surname)

        surname_consonants = self._get_consonants(surname)
        cons_len = len(surname_consonants)

        if cons_len < 3:
            surname_part = "".join(surname_consonants + self._get_vowels(surname))[:3]
        else:
            surname_part = "".join(surname_consonants)[:3]
        return surname_part

    def _transliterate_name(self, name: str) -> str:
        nfkd_form: str = unicodedata.normalize("NFKD", name)
        return "".join([c for c in nfkd_form if unicodedata.combining(c) == 0])

    def _get_vowels(self, sequence: str) -> list:
        """
        Returns list of vowels in provided string
        """
        vowels = []
        for char in sequence:
            if char in VOWELS:
                vowels.append(char)
        return vowels

    def _get_consonants(self, sequence: str) -> list:
        """
        Returns list of consonants in provided string
        """
        consonants = []
        for char in sequence:
            if char in CONSONANTS:
                consonants.append(char)
        return consonants

    def _pad_shorter(self, sequence: str) -> str:
        """
        Pads shorter string with the allowed char
        """
        return sequence.ljust(3, "X")

    @staticmethod
    def is_leap_year(year: int) -> bool:
        """
        Checks if the one given is a leap year
        """
        if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
            return True
        return False

    def _get_max_day(self, is_leap_year: bool, month: str) -> int:
        """
        Returns the maximum day for the current month
        """
        if month in ["D", "H", "P", "S"]:
            max_day = 30
        elif month == "B":
            max_day = 29 if is_leap_year else 28
        else:
            max_day = 31
        return max_day
