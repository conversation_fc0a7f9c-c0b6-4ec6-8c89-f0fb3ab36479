from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "<PERSON><PERSON><PERSON>",
        "1": "Ponedeljek",
        "2": "<PERSON><PERSON>",
        "3": "<PERSON><PERSON>",
        "4": "<PERSON><PERSON><PERSON><PERSON>",
        "5": "<PERSON><PERSON>",
        "6": "<PERSON><PERSON><PERSON>",
    }

    MONTH_NAMES = {
        "01": "<PERSON><PERSON><PERSON>",
        "02": "<PERSON><PERSON><PERSON>",
        "03": "<PERSON><PERSON>",
        "04": "April",
        "05": "<PERSON>",
        "06": "Junij",
        "07": "Julij",
        "08": "Avgust",
        "09": "September",
        "10": "<PERSON><PERSON><PERSON>",
        "11": "November",
        "12": "December",
    }

    def day_of_week(self) -> str:
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self) -> str:
        month = self.month()
        return self.MONTH_NAMES[month]
