import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=60)
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'

    # Configuration Gemini AI
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
    GEMINI_MODEL = os.environ.get('GEMINI_MODEL') or 'gemini-2.0-flash-exp'
    GEMINI_TEMPERATURE = float(os.environ.get('GEMINI_TEMPERATURE', '0.7'))
    GEMINI_MAX_TOKENS = int(os.environ.get('GEMINI_MAX_TOKENS', '2048'))
    GEMINI_TOP_P = float(os.environ.get('GEMINI_TOP_P', '0.8'))
    GEMINI_TOP_K = int(os.environ.get('GEMINI_TOP_K', '40'))

    # Configuration du support AI
    AI_SUPPORT_ENABLED = os.environ.get('AI_SUPPORT_ENABLED', 'True').lower() == 'true'
    AI_CONFIDENCE_THRESHOLD = float(os.environ.get('AI_CONFIDENCE_THRESHOLD', '0.7'))
    AI_MAX_CONVERSATION_LENGTH = int(os.environ.get('AI_MAX_CONVERSATION_LENGTH', '20'))
    AI_RESPONSE_TIMEOUT = int(os.environ.get('AI_RESPONSE_TIMEOUT', '30'))
    AI_AUTO_ESCALATION = os.environ.get('AI_AUTO_ESCALATION', 'True').lower() == 'true'
    AI_RETRY_ATTEMPTS = int(os.environ.get('AI_RETRY_ATTEMPTS', '3'))
    AI_RETRY_DELAY = float(os.environ.get('AI_RETRY_DELAY', '1.0'))