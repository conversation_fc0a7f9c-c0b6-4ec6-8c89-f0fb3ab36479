from datetime import datetime

from .. import Provider as CompanyProvider


def calculate_checksum(value: str) -> str:
    factors = [3, 7, 2, 4, 10, 3, 5, 9, 4, 6, 8][-len(value) :]
    check_sum = 0
    for number, factor in zip(value, factors):
        check_sum += int(number) * factor

    return str((check_sum % 11) % 10)


class Provider(CompanyProvider):
    formats = (
        "{{company_prefix}} «{{last_name}}»",
        "{{company_prefix}} «{{last_name}} {{last_name}}»",
        "{{company_prefix}} «{{last_name}}-{{last_name}}»",
        "{{company_prefix}} «{{last_name}}, {{last_name}} и {{last_name}}»",
        "{{last_name}} {{company_suffix}}",
        "{{large_company}}",
    )

    company_prefixes = (
        "РАО",
        "АО",
        "ИП",
        "НПО",
        "ЗАО",
        "ООО",
        "ОАО",
    )

    company_suffixes = (
        "Инк",
        "Инкорпорэйтед",
        "и партнеры",
        "Групп",
        "Лтд",
        "Лимитед",
    )

    # Source: https://www.rbc.ru/rbc500/
    large_companies = (
        "Газпром",
        "ЛУКОЙЛ",
        "Роснефть",
        "Сбербанк России",
        "Российские железные дороги",
        "Ростех",
        "Сургутнефтегаз",
        "X5 Retail Group",
        "ВТБ",
        "Магнит",
        "САФМАР",
        "Росатом",
        "Российские сети",
        "Интер РАО",
        "Транснефть",
        "Татнефть",
        "НОВАТЭК",
        "Евраз",
        "АФК Система",
        "En +",
        "НЛМК",
        "Норникель",
        "ГК Мегаполис",
        "Газпромбанк",
        "Русал",
        "Аэрофлот — Российские авиалинии",
        "Сибур Холдинг",
        "Северсталь",
        "СУЭК",
        "ММК",
        "Группа УГМК",
        "Мобильные телесистемы",
        "Металлоинвест",
        "Лента",
        "Объединенная авиастроительная корпорация",
        "РусГидро",
        "Сахалин Энерджи",
        "Т Плюс",
        "Группа М.Видео-Эльдорадо",
        "Еврохим",
        "ВымпелКом",
        "Банковский холдинг Альфа-банка",
        "Объединенная судостроительная корпорация",
        "МегаФон",
        "Ростелеком",
        "ТМК",
        "Славнефть",
        "Тойота Мотор (Toyota)",
        "Мечел",
        "Автотор холдинг",
        "Стройгазмонтаж",
        "Дж.Т.И. Россия (JTI)",
        "Торговая сеть Красное и Белое",
        "АК Алроса",
        "Дикси Групп",
        "ВЭБ.РФ",
        "ФМСМ (PMI)",
        "Фольксваген Груп Рус",
        "АвтоВАЗ",
        "Леруа Мерлен Восток (Leroi Merlin)",
        "Ашан (Auchan)",
        "Россельхозбанк",
        "ДНС Групп",
        "ГК ТНС энерго",
        "Протек",
        "Группа компаний ПИК",
        "Объединенная двигателестроительная корпорация",
        "Независимая нефтегазовая компания",
        "Merlion",
        "ФосАгро",
        "КМР и СНГ (KIA)",
        "Катрен",
        "Банк ФК Открытие",
        "Корпорация Тактическое ракетное вооружение",
        "Группа Рольф",
        "ТАИФ-НК",
        "Трансмашхолдинг",
        "Метро Кэш энд Керри (Metro Cash & Carry)",
        "Мостотрест",
        "СОГАЗ",
        "Эппл Рус (Apple)",
        "Арктикгаз",
        "Нижнекамскнефтехим",
        "«Томскнефть» ВНК",
        "Зарубежнефть",
        "ЕвроСибЭнерго",
        "Вертолеты России",
        "Группа ГАЗ",
        "Почта России",
        "МУМТ (BAT)",
        "Стройтранснефтегаз",
        "КамАЗ",
        "ФК Пульс",
        "Полюс",
        "Хендэ Мотор СНГ (Hyundai)",
        "S7 Group",
        "Ямал СПГ",
        "Группа Содружество",
        "ЧТПЗ",
        "Иркутская нефтяная компания",
        "Русснефть",
        "Национальная компьютерная корпорация",
        "Мерседес-Бенц Рус (Mercedes-Benz)",
        "Русэнергосбыт",
        "ОМК",
        "Уралкалий",
        "ГК Ташир",
        "Компания Газ-Альянс",
        "ФортеИнвест",
        "Группа Мэйджор",
        "Российская электроника",
        "ГК СНС",
        "Сибирский антрацит",
        "Группа О'кей",
        "Мосинжпроект",
        "UCL Holding",
        "Группа Илим",
        "Московский кредитный банк",
        "Группа Синара",
        "Нефтиса",
        "Объединенная компания Связной — Евросеть",
        "Группа ЛСР",
        "Т2 РТК Холдинг",
        "НЗНП",
        "АльфаСтрахование",
        "Ланит",
        "НПК Уралвагонзавод",
        "Рено Россия (Renault)",
        "Удмуртнефть",
        "Нестле Россия (Nestle)",
        "Райффайзенбанк (Raiffeisen)",
        "Техкомпания Хуавэй (Huawei)",
        "КДВ Групп",
        "Яндекс",
        "Мессояханефтегаз",
        "БМВ Русланд Трейдинг (BMW)",
        "Салым Петролеум",
        "Данон  (Danone)",
        "ЮниКредит Банк (UniCredit)",
        "ТД Риф",
        "Мираторг",
        "Группа Волга-Днепр",
        "Вайлдберриз",
        "Московский метрополитен",
        "Полиметалл",
        "Группа РЕСО",
        "Пепсико холдингс",
        "ГК Эфко",
        "СДС-Уголь",
        "ЛокоТех",
        "ГК Автомир",
        "Совкомбанк",
        "ФСК Лидер",
        "Марс (Mars)",
        "Детский мир",
        "Группа НПФ Благосостояние",
        "Госкорпорация по ОрВД",
        "Трансойл",
        "ОХК Уралхим",
        "Каспийский трубопроводный консорциум-Р",
        "Тинькофф Банк",
        "Fix Price",
        "Промсвязьбанк",
        "Акрон",
        "Спортмастер",
        "Проктер Энд Гэмбл. Дистрибьюторская компания (Procter & Gamble)",
        "Eurasia Drilling Company",
        "Группа Черкизово",
        "ИКЕА Дом (INGKA)",
        "Славянск Эко",
        "Корпорация ВСМПО-АВИСМА",
        "Росбанк (Societe General)",
        "Монетка",
        "Стройсервис",
        "ГК Транстехсервис",
        "Совкомфлот",
        "ВСК",
        "СБСВ-Ключавто",
        "Ингосстрах",
        "Сэтл групп",
        "Гиперглобус (Bruch-Beteiligungs)",
        "Технониколь",
        "Металлсервис",
        "Нефтехимсервис",
        "Промышленно-металлургический холдинг",
        "Урало-Сибирская металлургическая компания",
        "Мария-Ра",
        "Globaltrans",
        "Кубанская нефтегазовая компания",
        "Авиакомпания ЮТэйр",
        "НПФ Газфонд пенсионные накопления",
        "Русагро",
        "Л'Этуаль",
        "ЛГ Электроникс Рус (LG)",
        "Каргилл (Cargill)",
        "ВАД",
        "Астон",
        "Уральские авиалинии",
        "Сталепромышленная компания",
        "НИПИ НГ Петон",
        "Бристоль",
        "Уралвтормет",
        "Нефтетранссервис",
        "Казаньоргсинтез",
        "Газпром бурение",
        "ГК Агро-Белогорье",
        "Фортум (Fortum)",
        "ПК Балтика (Carlsbergfondet)",
        "Авилон АГ",
        "Шелл Нефть (Shell)",
        "Юнипро (Uniper)",
        "Технологии машиностроения (Техмаш)",
        "НПК Объединенная вагонная компания",
        "Велесстрой",
        "ТД Интерторг",
        "Юнилевер Русь (Unilever)",
        "Солид-товарные рынки",
        "Вольво Восток (AB Volvo)",
        "Энел Россия",
        "Марвел КТ",
        "ГК Эталон",
        "Металлокомплект-М",
        "Группа Ренессанс Страхование",
        "Военторг",
        "Nordgold",
        "Сибуглемет",
        "Акционерный банк Россия",
        "ДОМ.РФ",
        "Форд Соллерс Холдинг",
        "ИКЕА Торг (INGKA)",
        "Макдоналдc (McDonald`s)",
        "Кузбасская топливная компания",
        "Хенкель Рус (Henkel)",
        "Дон-Строй Инвест",
        "Главное управление обустройства войск (ГУОВ)",
        "СК Росгосстрах",
        "Кока-Кола Эйчбиси Евразия (Coca-Cola)",
        "Хоум Кредит энд Финанс Банк (PPF)",
        "Гленкор Агро Мзк (Firada)",
        "Mail.Ru Group",
        "Монди СЛПК (Mondi)",
        "НПО Алмаз",
        "ММС Рус (Mitsubishi Motors)",
        "Объединенные кондитеры",
        "Комацу СНГ (Komatsu)",
        "Национальная медиа группа",
        "Агентство по страхованию вкладов (АСВ)",
        "Татэнергосбыт",
        "Куйбышевазот",
        "Азбука вкуса",
        "Трансбункер",
        "Башкирская содовая компания",
        "Инвестнефтетрейд",
        "Inventive Retail Group",
        "Самсунг Электроникс Рус Калуга (Samsung)",
        "Крокус",
        "Гугл (Google)",
        "АСЦ-Холдинг",
        "Новороссийский морской торговый порт",
        "Швабе",
        "Русская медная компания",
        "Евроцемент груп",
        "Мосводоканал",
        "Международный аэропорт Шереметьево",
        "Сегежа",
        "Р-Фарм",
        "Фармстандарт",
        "Ростсельмаш",
        "Транспортная группа FESCO",
        "Компания Адамас",
        "Метафракс",
        "Джонсон & Джонсон (Johnson & Johnson)",
        "Softline",
        "Ягуар ленд ровер",
        "Байер",
        "Эркафарм",
        "Фармперспектива",
        "Банк Уралсиб",
        "ВО Машиноимпорт",
        "Кордиант",
        "Новосталь",
        "ВкусВилл",
        "Л'Ореаль (L'Oreal)",
        "DDS",
        "ТОАЗ",
        "Банк Санкт-Петербург",
        "Группа агропредприятий Ресурс",
        "Ярче!",
        "Ренейссанс Констракшн (Ronesans Holding Anonim Sirketi)",
        "Санофи Россия (Sanofi)",
        "Группа ГМС",
        "Северный ветер",
        "БСС",
        "Скания-Русь (Scania)",
        "ГК Фаворит Моторс",
        "Группа РТК",
        "Фармкомплект",
        "Нокиан Шина (Nokian)",
        "ДСК Автобан",
        "Омега Групп",
        "Квадра",
        "Roust",
        "ГК Невада (Самбери)",
        "Восточный экспресс банк",
        "Верисел-трейдинг",
        "Гознак",
        "Фирма Агрокомплекс им. Ткачева",
        "Банк Русский стандарт",
        "Мазда Мотор Рус (Mazda)",
        "Группа Газфонд",
        "СТД Петрович",
        "Беркс",
        "Кари",
        "Арконик СМЗ",
        "Мон Дэлис (Mondelez)",
        "Комус",
        "Группа Агат",
        "Великолукский мясокомбинат",
        "Верный",
        "СДС Азот",
        "М Фэшн",
        "Белгранкорм-холдинг",
        "Группа Нэфис",
        "ФГ Будущее",
        "Глория Джинс",
        "Билла (Rewe)",
        "Государственная транспортная лизинговая компания",
        "ФК Гранд Капитал",
        "ЭС",
        "Компания Металл Профиль",
        "ГК Орими Трэйд",
        "ГСЛ",
        "Интернешнл Пейпер (International Paper)",
        "Лаборатория Касперского",
        "ПСМА Рус",
        "Аптечная сеть 36,6",
        "Тетра Пак (Tetra Pak)",
        "Центральная пригородная пассажирская компания",
        "Самараэнерго",
        "Азур Эйр",
        "Командор-Холдинг",
        "Белуга Групп",
        "ТД БелАЗ",
        "Мосгортранс",
        "Спар Миддл Волга",
        "Холдинг Транспортные компоненты",
        "Московский аэропорт Домодедово",
        "Рулог (Havi)",
        "Эйч Энд Эм (H&M)",
        "Концерн Автоматика",
        "Татэнерго",
        "Трубная грузовая компания",
        "Комос Групп",
        "Первая тяжеловесная компания",
        "ОМПК",
        "НК Дулисьма",
        "Ачимгаз",
        "Новосибирскэнергосбыт",
        "Компания СИМ-Авто",
        "Ситибанк",
        "Остин",
        "Адидас (Adidas)",
        "Ферреро Руссия (Ferrero)",
        "Пермэнергосбыт",
        "РКК Энергия",
        "Свеза",
        "Росжелдорпроект",
        "Мазда Соллерс Мануфэкчуринг Рус",
        "БСХ Бытовые приборы  (BSH Hausgerate)",
        "Московская биржа ММВБ-РТС",
        "Русэнергоресурс",
        "Компания Луис Дрейфус Восток (Louis Dreyfus)",
        "ЭР-Телеком Холдинг",
        "Соллерс",
        "Объединенная энергетическая компания",
        "Уральские локомотивы",
        "ТМК Чермет",
        "Загорский трубный завод",
        "Элко Рус (Elko)",
        "Архангельский ЦБК",
        "Мособлгаз",
        "ДК Рус",
        "Энергосбытовая компания Восток",
        "ГКНПЦ им. М.В.Хруничева",
        "Металлоторг",
        "Агросила Групп",
        "Ман Трак Энд Бас Рус (Volkswagen)",
        "Петербургский метрополитен",
        "ТГК-2",
        "Концерн Титан-2",
        "Ренейссанс Хэви Индастрис Ronesans Endustri",
        "Бургер Рус (Burger King)",
        "Ozon",
        "Сони Электроникс (Sony)",
        "Продо",
        "Продимекс-Холдинг",
        "АвтоГермес",
        "Railgo",
        "Новотранс",
        "Новикомбанк",
        "Рив Гош",
        "Сибирская горно-металлургическая компания",
        "Сименс (Siemens)",
        "Лига ставок",
        "Банк Ак Барс",
        "Группа Полипластик",
        "Водоканал Санкт-Петербурга",
        "РэйлАльянс",
        "Российская телевизионная и радиовещательная сеть",
        "Зерно-трейд",
        "Ренессанс Кредит",
        "Роберт Бош (Robert Bosch)",
        "ВО Промсырьеимпорт",
        "САП СНГ (SAP)",
        "А Групп",
        "Приосколье",
        "Зара СНГ (Zara)",
        "Модум-транс",
        "Эбботт лэбораториз (Abbott Laboratories)",
        "Группа Магнезит",
        "Газпром автоматизация",
        "Газэнергосервис",
        "Независимая энергосбытовая компания Краснодарского края",
        "Группа ЭПМ",
        "Минудобрения",
        "Либхерр-Русланд (Liebherr)",
        "Восточная техника (Vost-Tech)",
        "Первый канал",
        "ГМК Сплав",
        "ГК Автодилерство",
        "НМЖК",
        "ВГТРК",
        "Неофарм",
        "Роскосмос",
        "Вита Лайн",
        "Краснодарзернопродукт-Экспо",
        "Алкоторг",
        "Красцветмет",
        "Касторама Рус (Castorama)",
        "Деловые линии",
        "ГВСУ по специальным объектам",
        "ПКФ ДиПОС",
        "Восток-Запад",
        "Амурская нефтебаза",
        "Юг Руси",
        "Шнейдер Электрик (Schneider Electric)",
        "Сингента (Chemchina)",
        "Титан",
        "Петропавловск",
        "Фармимэкс",
        "АБ Инбев Эфес (Anheuser-Busch Inbev)",
        "ABI Product",
        "Профитмед",
        "ТД Агроторг",
        "ТЭК СПБ",
        "ТД Ункомтех",
        "ОПХ (Heineken)",
        "ТГК-16",
        "Уральский банк реконструкции и развития",
        "QIWI",
        "СК Согласие",
        "Группа Эссен",
        "Втормет",
        "Эссити (Essity)",
        "Hoff (Домашний интерьер)",
        "Сиско Солюшенз (Cisco)",
        "ВО ЖДТ России",
        "Купишуз (Lamoda)",
        "Делл (Dell)",
        "ПСК",
        "Каменск-Уральский металлургический завод",
        "Аргос",
        "А.П.Р.",
        "ГК 1520",
        "Артис-Агро Экспорт",
        "Луидор",
        "Порше Руссланд (Porsche)",
        "Денцу Эйджис Си Эс (Dentsu)",
        "Эйвон Бьюти Продактс Компани (Avon)",
        "РКЦ Прогресс",
        "Силовые машины",
        "АНГК",
        "Корпорация Гринн",
        "Фаберлик",
        "Сибирская сервисная компания",
        "Банк Возрождение",
        "Отисифарм",
        "Боэс Констракшн (Boes Construction)",
        "Саткинский чугуноплавильный завод",
        "Алтайвагон",
        "ПТК",
        "Щекиноазот",
        "Волгоградэнергосбыт",
        "Русский уголь",
        "Трест КХМ",
        "РМ Рейл",
        "Восточная горнорудная компания",
        "Группа Стройтрансгаз",
        "БАСФ (BASF)",
        "Мерида",
        "Брок-Инвест-Сервис и К",
        "Вирлпул Рус (Whirlpool)",
        "Карелия Палп",
        "Тева (Teva)",
        "Media Direction Group",
        "Якобс Дау Эгбертс Рус (Jacobs Douwe Egberts)",
        "ГК Великан",
        "Август",
        "Транслом",
        "ОТП Банк",
        "РусВинил",
        "Системный оператор Единой энергетической системы",
        "АСР-Углесбыт",
        "ЦЭНКИ",
        "Транстрейдойл",
        "Росморпорт",
        "Газнефтетрэйдинг",
        "Сладковско-Заречное",
        "Кроношпан (Kronoplus)",
        "ТЦ Кунцево Лимитед",
        "СНПХ",
        "Кимберли-Кларк (Kimberly-Clark)",
        "Катерпиллар Евразия (Caterpillar)",
        "Крок инкорпорейтед",
        "Ашинский металлургический завод",
        "Автодом",
        "Международный центр",
        "Мишлен (Michelin)",
        "Картли",
        "БелАЗ-24",
        "Первый завод",
        "ГК ЕКС",
        "Петролеум Трейдинг",
        "Нижфарм (Nidda Midco)",
        "Импэкснефтехим",
        "Вольво Карс (Zhejiang Geely)",
        "Мосметрострой",
        "ТЭК Мосэнерго",
        "Борисхоф 1 (Inchcape)",
        "ГК Титан",
        "ПТК Уголь",
        "Авторусь",
        "Юг-Авто",
        "Нова",
        "Метрострой",
        "Ресурс",
        "Сетевая компания",
        "РЕ Трэйдинг (LPP)",
        "Углетранс",
        "ЭйчПи Инк (HP Inc.)",
        "ТК Шлюмберже (Schlumberger)",
        "ГК Мега-Авто",
        "Корпорация Электросевкавмонтаж",
        "ГК Российские коммунальные системы",
        "Запсибгазпром",
        "Нефтепродукттрейд",
        "Сатурн-Р",
        "Завод имени Дегтярева",
        "Такеда Фармасьютикалс (Takeda Pharmaceutical)",
        "Слата супермаркет",
        "Emex",
        "САМ-МБ",
        "171 Меридиан",
        "Армтек",
        "Центр финансовых технологий",
        "Группа компаний Пионер",
        "АХ Степь",
        "Таграс (ТНГ-Групп)",
        "Fonbet",
        "Сандоз (Sandoz)",
        "Берлин-Хеми А. Менарини (Berlin Chemie)",
        "ГК Агропромкомплектация",
        "МАКС",
        "Компания Трасса",
        "Башкирэнерго",
        "Охрана Росгвардии",
        "Гала-Форм",
        "КРКА Фарма (KRKA)",
        "Максидом",
        "Нефтехимремстрой",
        "Нефтьмагистраль",
        "Авеста Фармацевтика (Baby Dream)",
        "Старттех",
        "Конар",
        "Нортгаз",
        "УГС",
        "АББ (ABB)",
        "Металлстандарт",
        "Балтийская топливная компания",
        "Мострансавто",
        "Аксель-Моторс",
        "Группа компаний МИЦ",
        "ПК Борец",
        "Европа",
        "Сибирская аграрная группа",
        "РТИ",
        "Ферронордик машины (Ferronordic)",
        "Южуралзолото ГК",
        "Прогресс",
        "Юг-Нефтепродукт",
        "Камский кабель",
        "Familia",
        "Транскапиталбанк",
        "А-Ойл",
        "Сибтрейд",
        "МТС-банк",
        "Московская инженерно-строительная компания",
        "Курганмашзавод",
        "Вектрум-К",
        "Морской терминал Тамань",
        "Таркетт Рус (Tarkett)",
        "Несте Санкт-Петербург (Neste)",
        "Ново-Уренгойская газовая компания",
        "Национальная нерудная компания",
        "Октоблу (Decathlon)",
        "Снежная Королева",
        "Новартис Фарма (Novartis)",
        "Магнолия",
        "Техинком",
        "Дочки-Сыночки",
        "Астеллас Фарма",
        "General Fueller",
        "Автозаправочные комплексы Atan",
        "Псковвтормет",
        "Авиакомпания Икар",
    )

    catch_phrase_adj = (
        (
            "Автоматизированный",
            "Автономный",
            "Адаптивный",
            "Амортизированный",
            "Ассимилированный",
            "Безопасный",
            "Бизнес-ориентированный",
            "Взаимовыгодный",
            "Виртуальный",
            "Глубокий",
            "Горизонтальный",
            "Делегируемый",
            "Децентрализованный",
            "Дублируемый",
            "Инверсный",
            "Инновационный",
            "Интегрированный",
            "Интуитивный",
            "Качественный",
            "Клиент-ориентированный",
            "Контролируемый",
            "Концептуальный",
            "Корпоративный",
            "Кросс-платформенный",
            "Межгрупповой",
            "Многогранный",
            "Многоканальный",
            "Многослойный",
            "Многоуровневый",
            "Модернизируемый",
            "Настраиваемый",
            "Новый",
            "Общедоступный",
            "Объектный",
            "Обязательный",
            "Оперативный",
            "Оптимизированный",
            "Опциональный",
            "Организованный",
            "Органичный",
            "Ориентированный",
            "Открытый",
            "Оцифрованный",
            "Переключаемый",
            "Переосмысленный",
            "Переработанный",
            "Перспективный",
            "Полный",
            "Поэтапный",
            "Превентивный",
            "Программируемый",
            "Прогрессивный",
            "Продвинутый",
            "Прочный",
            "Разнообразный",
            "Распределённый",
            "Расширенный",
            "Реализованный",
            "Реконструируемый",
            "Самодостаточный",
            "Сбалансированный",
            "Сетевой",
            "Синхронизированный",
            "Совместимый",
            "Сокращенный",
            "Сосредоточенный",
            "Стабильный",
            "Стратегический",
            "Увеличенный",
            "Удобный",
            "Улучшенный",
            "Улучшенный",
            "Уменьшенный",
            "Универсальный",
            "Управляемый",
            "Устойчивый",
            "Фундаментальный",
            "Функциональный",
            "Цельный",
            "Централизованный",
            "Эксклюзивный",
            "Элегантный",
            "Эргономичный",
        ),
        (
            "аналитический",
            "асимметричный",
            "асинхронный",
            "бездефектный",
            "бескомпромиссный",
            "веб-ориентированный",
            "встречный",
            "вторичный",
            "высокоуровневый",
            "гибкий",
            "гибридный",
            "глобальный",
            "двунаправленный",
            "действенный",
            "динамичный",
            "единообразный",
            "заметный",
            "инструктивный",
            "интерактивный",
            "исполнительный",
            "итернациональный",
            "клиент-серверный",
            "контекстуальный",
            "круглосуточный",
            "логистический",
            "локальный",
            "максимальный",
            "масштабируемый",
            "методичный",
            "многозадачный",
            "мобильный",
            "модульный",
            "мультимедийный",
            "наглядный",
            "направленный",
            "национальный",
            "нейтральный",
            "нестандартный",
            "объектно-ориентированный",
            "однородный",
            "оптимальный",
            "основной",
            "отказостойкий",
            "переходный",
            "последовательный",
            "потенциальный",
            "пошаговый",
            "прибыльный",
            "приоритетный",
            "промежуточный",
            "радикальный",
            "раздвоенный",
            "региональный",
            "связный",
            "систематический",
            "системный",
            "составной",
            "социальный",
            "специализированный",
            "статический",
            "третичный",
            "ультрасовременный",
            "целостный",
            "широкий",
            "широкопрофильный",
            "эвристический",
            "экоцентричный",
            "энергонезависимый",
            "яркий",
        ),
    )

    catch_phrase_nouns_masc = (
        "адаптер",
        "алгоритм",
        "альянс",
        "анализатор",
        "архив",
        "веб-сайт",
        "вызов",
        "графический интерфейс",
        "графический интерфейс пользователя",
        "доступ",
        "инструментарий",
        "интерфейс",
        "инфопосредник",
        "искусственный интеллект",
        "массив",
        "модератор",
        "мониторинг",
        "набор инструкций",
        "параллелизм",
        "подход",
        "портал",
        "прогноз",
        "продукт",
        "проект",
        "протокол",
        "ресурс",
        "системный движок",
        "успех",
        "фреймворк",
        "хаб",
        "эталон",
    )

    catch_phrase_nouns_fem = (
        "архитектура",
        "база данных",
        "база знаний",
        "вероятность",
        "возможность",
        "гибкость",
        "защищенная линия",
        "иерархия",
        "инициатива",
        "инфраструктура",
        "кодировка",
        "конгломерация",
        "концепция",
        "координация",
        "локальная сеть",
        "матрица",
        "методология",
        "миграция",
        "модель",
        "нейронная сеть",
        "парадигма",
        "поддержка",
        "политика",
        "проекция",
        "производительность",
        "прошивка",
        "рабочая группа",
        "реализация",
        "сеть Интранет",
        "сеть Экстранет",
        "служба поддержки",
        "служба техподдержки",
        "способность",
        "стандартизация",
        "стратегия",
        "структура",
        "суперструктура",
        "установка",
        "фокус-группа",
        "функциональность",
        "функция",
        "ценовая структура",
        "эмуляция",
    )

    catch_phrase_nouns_neu = (
        "взаимодействие",
        "групповое программное обеспечение",
        "интернет-решение",
        "использование",
        "межплатформенное программное обеспечение",
        "оборудование",
        "определение",
        "отношение",
        "приложение",
        "программное обеспечение",
        "решение",
        "совершенствование процесса",
        "сотрудничество",
        "управление бюджетом",
        "хранилище данных",
        "шифрование",
        "ядро",
    )

    bsWords = (
        (
            "Адаптация",
            "Визуализация",
            "Включение",
            "Внедрение",
            "Генерация",
            "Инновация",
            "Интеграция",
            "Использование",
            "Итерация",
            "Конструирование",
            "Координация",
            "Культивация",
            "Максимизация",
            "Модернизация",
            "Монетизация",
            "Мотивация",
            "Обеспечение",
            "Объединение",
            "Оптимизация",
            "Освоение",
            "Охват",
            "Оцифровка",
            "Перезагрузка",
            "Переопределение",
            "Переосмысление",
            "Перепрофилирование",
            "Переход",
            "Преображение",
            "Приспособление",
            "Продление",
            "Производство",
            "Развитие",
            "Разворачивание",
            "Разработка",
            "Распределение",
            "Реализация",
            "Революция",
            "Синтез",
            "Синхронизация",
            "Сравнение",
            "Трансформация",
            "Увеличение",
            "Управление",
            "Ускорение",
            "Формирование",
            "Шкалирование",
            "Эксплуатация",
        ),
        (
            "B2B",
            "B2C",
            "активных",
            "безотказных",
            "беспроводных",
            "богатых",
            "веб-ориентированных",
            "вертикальных",
            "виртуальных",
            "глобальных",
            "действенных",
            "динамичных",
            "заказных",
            "индивидуальных",
            "инновационных",
            "интегрированных",
            "интерактивных",
            "интуитивных",
            "концептуальных",
            "корпоративных",
            "критически важных",
            "кроссплатформенных",
            "круглогодичных",
            "круглосуточных",
            "лучших в своём роде",
            "масштабируемых",
            "мультимедийных",
            "наглядных",
            "надежных",
            "онлайн и офлайн",
            "ориентированных на пользователя",
            "открытых",
            "передовых",
            "подробных",
            "популярных",
            "престижных",
            "прибыльных",
            "притягательных",
            "прозрачных",
            "распределённых",
            "распространенных",
            "расширяемых",
            "революционных",
            "сенсационных",
            "серверных",
            "сетевых",
            "соблазнительных",
            "совместных",
            "современных",
            "стандартных",
            "стратегических",
            "ультрасовременных",
            "фронт-энд",
            "целостных",
            "цельных",
            "эффективных",
        ),
        (
            "архитектур",
            "аудиторий",
            "веб-сервисов",
            "взаимодействий",
            "действий",
            "диапазонов",
            "знаний",
            "инициатив",
            "интернет-компаний",
            "интернет-магазинов",
            "интернет-продавцов",
            "интернет-услуг",
            "интерфейсов",
            "инфопосредников",
            "инфраструктур",
            "каналов",
            "методик",
            "метрик",
            "моделей",
            "ниш",
            "областей интереса",
            "отношений",
            "парадигм",
            "партнерств",
            "платформ",
            "пользователей",
            "порталов",
            "приложений",
            "результатов",
            "решений",
            "рынков",
            "сетей",
            "систем",
            "систем снабжения",
            "сообществ",
            "схем",
            "технологий",
            "функций",
        ),
    )

    def catch_phrase(self) -> str:
        """
        :example: 'Адаптивный и масштабируемый графический интерфейс'
        """
        noun: str = self.random_element(
            self.catch_phrase_nouns_masc + self.catch_phrase_nouns_fem + self.catch_phrase_nouns_neu
        )
        adj_first: str = self.random_element(self.catch_phrase_adj[0])
        adj_second: str = self.random_element(self.catch_phrase_adj[1])
        if noun in self.catch_phrase_nouns_fem:
            adj_first = adj_first[:-2] + "ая"
            adj_second = adj_second[:-2] + "ая"
        elif noun in self.catch_phrase_nouns_neu:
            adj_first = adj_first[:-2] + "ое"
            adj_second = adj_second[:-2] + "ое"
        return adj_first + " и " + adj_second + " " + noun

    def large_company(self) -> str:
        """
        :example: 'АвтоВАЗ'
        """
        return self.random_element(self.large_companies)

    def company_prefix(self) -> str:
        """
        :example: 'ООО'
        """
        return self.random_element(self.company_prefixes)

    def businesses_inn(self) -> str:
        """
        Returns tax identification number for businesses (ru. идентификационный номер налогоплательщика, ИНН).
        """
        region: str = "%02d" % self.random_int(min=1, max=92)
        inspection: str = "%02d" % self.random_int(min=1, max=99)
        tail: str = "%05d" % self.random_int(min=1, max=99999)
        result: str = region + inspection + tail

        return result + calculate_checksum(result)

    def individuals_inn(self) -> str:
        """
        Returns tax identification number for individuals (ru. идентификационный номер налогоплательщика, ИНН).
        """
        region: str = "%02d" % self.random_int(min=1, max=92)
        inspection: str = "%02d" % self.random_int(min=1, max=99)
        tail: str = "%06d" % self.random_int(min=1, max=999999)
        result: str = region + inspection + tail
        result += calculate_checksum(result)

        return result + calculate_checksum(result)

    def businesses_ogrn(self) -> str:
        """
        Returns primary state registration number for businesses
        (ru. основной государственный регистрационный номер, ОГРН).
        """
        sign: str = self.random_element(("1", "5"))
        year: str = "%02d" % self.random_int(min=1, max=datetime.now().year - 2000)
        region: str = "%02d" % self.random_int(min=1, max=92)
        tail: str = "%07d" % self.random_int(min=1, max=9999999)

        result: str = sign + year + region + tail

        return result + str((int(result) % 11) % 10)

    def individuals_ogrn(self) -> str:
        """
        Returns primary state registration number for individuals
        (ru. основной государственный регистрационный номер, ОГРН).
        """
        year: str = "%02d" % self.random_int(min=1, max=datetime.now().year - 2000)
        region: str = "%02d" % self.random_int(min=1, max=92)
        tail: str = "%09d" % self.random_int(min=1, max=*********)

        result: str = "3" + year + region + tail

        return result + str((int(result) % 13) % 10)

    def kpp(self) -> str:
        """
        Returns tax registration reason code (ru. код причины постановки на учет, КПП).
        """
        region: str = "%02d" % self.random_int(min=1, max=92)
        inspection: str = "%02d" % self.random_int(min=1, max=99)
        reason: str = self.random_element(("01", "43", "44", "45"))
        tail: str = "%03d" % self.random_int(min=1, max=999)

        return region + inspection + reason + tail
