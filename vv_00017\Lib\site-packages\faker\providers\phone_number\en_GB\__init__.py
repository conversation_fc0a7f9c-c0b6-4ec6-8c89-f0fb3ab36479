from .. import Provider as PhoneNumberProvider


class Provider(PhoneNumberProvider):
    # Source:
    # https://en.wikipedia.org/wiki/Telephone_numbers_in_the_United_Kingdom
    # Fake phone numbers should be fake - this provider has been rewritten to
    # use numbers reserved for dramatic use by Ofcom. See the following:
    # https://en.wikipedia.org/wiki/Fictitious_telephone_number#United_Kingdom
    # This ensures no genuine numbers are generated at random.
    #
    # It's worth noting that the following examples include incorrect notation
    # of British phone numbers.  +44(0)xxx is incorrect and the '(0)' should
    # be omitted.  However, it's commonly written this way by Joe <PERSON>
    # and would better serve this project to be included, as it represents
    # more realistic data and is of benefit to those developing data cleansing
    # tools etc.  All possible official fake numbers are covered below.

    cellphone_formats = (
        "07700 900 ###",
        "07700 900###",
        "07700900###",
        "(07700) 900 ###",
        "(07700) 900###",
        "(07700)900###",
        "+447700 900 ###",
        "+447700 900###",
        "+447700900###",
        "+44(0)7700 900 ###",
        "+44(0)7700 900###",
        "+44(0)7700900###",
    )

    formats = (
        "0113 496 0###",
        "0113 4960###",
        "01134960###",
        "(0113) 496 0###",
        "(0113) 4960###",
        "(0113)4960###",
        "+44113 496 0###",
        "+44113 4960###",
        "+441134960###",
        "+44(0)113 496 0###",
        "+44(0)113 4960###",
        "+44(0)1134960###",
        "0114 496 0###",
        "0114 4960###",
        "01144960###",
        "(0114) 496 0###",
        "(0114) 4960###",
        "(0114)4960###",
        "+44114 496 0###",
        "+44114 4960###",
        "+441144960###",
        "+44(0)114 496 0###",
        "+44(0)114 4960###",
        "+44(0)1144960###",
        "0115 496 0###",
        "0115 4960###",
        "01154960###",
        "(0115) 496 0###",
        "(0115) 4960###",
        "(0115)4960###",
        "+44115 496 0###",
        "+44115 4960###",
        "+441154960###",
        "+44(0)115 496 0###",
        "+44(0)115 4960###",
        "+44(0)1154960###",
        "0116 496 0###",
        "0116 4960###",
        "01164960###",
        "(0116) 496 0###",
        "(0116) 4960###",
        "(0116)4960###",
        "+44116 496 0###",
        "+44116 4960###",
        "+441164960###",
        "+44(0)116 496 0###",
        "+44(0)116 4960###",
        "+44(0)1164960###",
        "0117 496 0###",
        "0117 4960###",
        "01174960###",
        "(0117) 496 0###",
        "(0117) 4960###",
        "(0117)4960###",
        "+44117 496 0###",
        "+44117 4960###",
        "+441174960###",
        "+44(0)117 496 0###",
        "+44(0)117 4960###",
        "+44(0)1174960###",
        "0118 496 0###",
        "0118 4960###",
        "01184960###",
        "(0118) 496 0###",
        "(0118) 4960###",
        "(0118)4960###",
        "+44118 496 0###",
        "+44118 4960###",
        "+441184960###",
        "+44(0)118 496 0###",
        "+44(0)118 4960###",
        "+44(0)1184960###",
        "0121 496 0###",
        "0121 4960###",
        "01214960###",
        "(0121) 496 0###",
        "(0121) 4960###",
        "(0121)4960###",
        "+44121 496 0###",
        "+44121 4960###",
        "+441214960###",
        "+44(0)121 496 0###",
        "+44(0)121 4960###",
        "+44(0)1214960###",
        "0131 496 0###",
        "0131 4960###",
        "01314960###",
        "(0131) 496 0###",
        "(0131) 4960###",
        "(0131)4960###",
        "+44131 496 0###",
        "+44131 4960###",
        "+441314960###",
        "+44(0)131 496 0###",
        "+44(0)131 4960###",
        "+44(0)1314960###",
        "0141 496 0###",
        "0141 4960###",
        "01414960###",
        "(0141) 496 0###",
        "(0141) 4960###",
        "(0141)4960###",
        "+44141 496 0###",
        "+44141 4960###",
        "+441414960###",
        "+44(0)141 496 0###",
        "+44(0)141 4960###",
        "+44(0)1414960###",
        "0151 496 0###",
        "0151 4960###",
        "01514960###",
        "(0151) 496 0###",
        "(0151) 4960###",
        "(0151)4960###",
        "+44151 496 0###",
        "+44151 4960###",
        "+441514960###",
        "+44(0)151 496 0###",
        "+44(0)151 4960###",
        "+44(0)1514960###",
        "0161 496 0###",
        "0161 4960###",
        "01614960###",
        "(0161) 496 0###",
        "(0161) 4960###",
        "(0161)4960###",
        "+44161 496 0###",
        "+44161 4960###",
        "+441614960###",
        "+44(0)161 496 0###",
        "+44(0)161 4960###",
        "+44(0)1614960###",
        "0191 498 0###",
        "0191 4960###",
        "01914960###",
        "(0191) 496 0###",
        "(0191) 4960###",
        "(0191)4960###",
        "+44191 496 0###",
        "+44191 4960###",
        "+441914960###",
        "+44(0)191 496 0###",
        "+44(0)191 4960###",
        "+44(0)1914960###",
        "020 7946 0###",
        "020 74960###",
        "02074960###",
        "(020) 7496 0###",
        "(020) 74960###",
        "(020)74960###",
        "+4420 7496 0###",
        "+4420 74960###",
        "+442074960###",
        "+44(0)20 7496 0###",
        "+44(0)20 74960###",
        "+44(0)2074960###",
        "028 9018 0###",
        "028 9018###",
        "0289018###",
        "(028) 9018 0###",
        "(028) 9018###",
        "(028)9018###",
        "+4428 9018 0###",
        "+4428 9018###",
        "+44289018###",
        "+44(0)28 9018 0###",
        "+44(0)28 9018###",
        "+44(0)289018###",
        "029 2018 0###",
        "029 2018###",
        "0292018###",
        "(029) 2018 0###",
        "(029) 2018###",
        "(029)2018###",
        "+4429 2018 0###",
        "+4429 2018###",
        "+44292018###",
        "+44(0)29 2018 0###",
        "+44(0)29 2018###",
        "+44(0)292018###",
        "01632 960 ###",
        "01632 960###",
        "01632960###",
        "(01632) 960 ###",
        "(01632) 960###",
        "(01632)960###",
        "+441632 960 ###",
        "+441632 960###",
        "+441632960###",
        "+44(0)1632 960 ###",
        "+44(0)1632 960###",
        "+44(0)1632960###",
        "0306 999 0###",
        "0306 9990###",
        "03069990###",
        "(0306) 999 0###",
        "(0306) 9990###",
        "(0306)9990###",
        "+44306 999 0###",
        "+44306 9990###",
        "+443069990###",
        "+44(0)306 999 0###",
        "+44(0)306 9990###",
        "+44(0)3069990###",
        "0808 157 0###",
        "0808 1570###",
        "08081570###",
        "(0808) 157 0###",
        "(0808) 1570###",
        "(0808)1570###",
        "+44808 157 0###",
        "+44808 1570###",
        "+448081570###",
        "+44(0)808 157 0###",
        "+44(0)808 1570###",
        "+44(0)8081570###",
        "0909 879 0###",
        "0909 8790###",
        "09098790###",
        "(0909) 879 0###",
        "(0909) 8790###",
        "(0909)8790###",
        "+44909 879 0###",
        "+44909 8790###",
        "+449098790###",
        "+44(0)909 879 0###",
        "+44(0)909 8790###",
        "+44(0)9098790###",
    )

    def cellphone_number(self) -> str:
        pattern: str = self.random_element(self.cellphone_formats)
        return self.numerify(self.generator.parse(pattern))
