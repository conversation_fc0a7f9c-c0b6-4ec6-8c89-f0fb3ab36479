<!doctype html>
<html>
  <head>
    <title>CodeMirror: R<PERSON> changes mode</title>
    <link rel="stylesheet" href="../../../lib/codemirror.css">
    <script src="../../../lib/codemirror.js"></script>
    <script src="changes.js"></script>
    <link rel="stylesheet" href="../../../doc/docs.css">
    <style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
  </head>
  <body>
    <h1>CodeMirror: RPM changes mode</h1>
    
    <div><textarea id="code" name="code">
-------------------------------------------------------------------
Tue Oct 18 13:58:40 UTC 2011 - <EMAIL>

- Update to r60.3
- Fixes bug in the reflect package
  * disallow Interface method on Value obtained via unexported name

-------------------------------------------------------------------
Thu Oct  6 08:14:24 UTC 2011 - <EMAIL>

- Update to r60.2
- Fixes memory leak in certain map types

-------------------------------------------------------------------
Wed Oct  5 14:34:10 UTC 2011 - <EMAIL>

- Tweaks for gdb debugging
- go.spec changes:
  - move %go_arch definition to %prep section
  - pass correct location of go specific gdb pretty printer and
    functions to cpp as HOST_EXTRA_CFLAGS macro
  - install go gdb functions & printer
- gdb-printer.patch
  - patch linker (src/cmd/ld/dwarf.c) to emit correct location of go
    gdb functions and pretty printer
</textarea></div>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: {name: "changes"},
        lineNumbers: true,
        indentUnit: 4,
        tabMode: "shift",
        matchBrackets: true
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-rpm-changes</code>.</p>
  </body>
</html>
