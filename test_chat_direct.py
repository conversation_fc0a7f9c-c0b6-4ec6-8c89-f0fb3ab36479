#!/usr/bin/env python3
"""
Test direct de l'API de chat pour identifier le problème
"""

import requests
import json

def test_chat_api_direct():
    """Teste l'API de chat directement"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Test direct de l'API de chat")
    print("=" * 40)
    
    # 1. Tester l'accès à la page de chat pour récupérer une conversation
    print("1. Accès à la page de chat...")
    try:
        session = requests.Session()
        
        # D'abord, essayer d'accéder à la page de chat
        response = session.get(f"{base_url}/support/chat")
        
        if response.status_code == 200:
            print("   ✅ Page de chat accessible")
            
            # Extraire l'ID de conversation du HTML (méthode simple)
            html_content = response.text
            
            # Chercher le champ conversationId dans le HTML
            import re
            match = re.search(r'id="conversationId" value="(\d+)"', html_content)
            
            if match:
                conversation_id = match.group(1)
                print(f"   ✅ ID de conversation trouvé: {conversation_id}")
                
                # 2. Tester l'envoi d'un message
                print(f"\n2. Test d'envoi de message...")
                
                message_data = {
                    "message": "Test de connexion",
                    "conversation_id": conversation_id
                }
                
                print(f"   📤 Envoi des données: {message_data}")
                
                response = session.post(
                    f"{base_url}/support/api/chat/send",
                    json=message_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                print(f"   📥 Code de réponse: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ Succès: {result}")
                else:
                    print(f"   ❌ Erreur: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"   📄 Détails: {error_data}")
                    except:
                        print(f"   📄 Contenu brut: {response.text}")
                
            else:
                print("   ❌ ID de conversation non trouvé dans le HTML")
                print("   🔍 Extrait du HTML:")
                # Afficher un extrait du HTML pour debug
                lines = html_content.split('\n')
                for i, line in enumerate(lines):
                    if 'conversationId' in line:
                        start = max(0, i-2)
                        end = min(len(lines), i+3)
                        for j in range(start, end):
                            print(f"      {j}: {lines[j]}")
                        break
                
        elif response.status_code == 302:
            print("   🔄 Redirection (connexion requise)")
            print("   💡 Vous devez être connecté pour tester l'API")
            
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")

def test_simple_api():
    """Test simple de l'API sans authentification"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("\n🔧 Test simple de l'API")
    print("=" * 40)
    
    # Test avec des données factices pour voir le type d'erreur
    test_data = {
        "message": "Test",
        "conversation_id": "1"
    }
    
    try:
        response = requests.post(
            f"{base_url}/support/api/chat/send",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Code de réponse: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Erreur 401 (non autorisé) - normal sans connexion")
        elif response.status_code == 400:
            print("❌ Erreur 400 (bad request) - problème de validation")
            try:
                error_data = response.json()
                print(f"Détails: {error_data}")
            except:
                print(f"Contenu: {response.text}")
        else:
            print(f"Réponse inattendue: {response.text}")
            
    except Exception as e:
        print(f"Erreur: {e}")

def main():
    """Fonction principale"""
    print("🚀 Diagnostic de l'API de chat")
    print("=" * 50)
    
    test_chat_api_direct()
    test_simple_api()
    
    print("\n" + "=" * 50)
    print("💡 Instructions:")
    print("1. Connectez-vous à l'interface web")
    print("2. Allez sur /support/chat")
    print("3. Ouvrez la console du navigateur")
    print("4. Essayez d'envoyer un message")
    print("5. Vérifiez les logs de l'application Flask")

if __name__ == "__main__":
    main()
