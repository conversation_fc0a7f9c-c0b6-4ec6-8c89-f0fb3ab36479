#!/usr/bin/env python3
"""
Script de test pour vérifier la configuration Gemini AI
Utilisez ce script pour diagnostiquer les problèmes de connexion
"""

import os
import sys
from dotenv import load_dotenv

def test_gemini_configuration():
    """Teste la configuration Gemini étape par étape"""
    
    print("🔍 Test de configuration Gemini AI")
    print("=" * 50)
    
    # 1. Charger les variables d'environnement
    load_dotenv()
    
    # 2. Vérifier la clé API
    api_key = os.environ.get('GEMINI_API_KEY')
    print(f"1. Clé API Gemini:")
    if not api_key:
        print("   ❌ GEMINI_API_KEY non trouvée dans les variables d'environnement")
        print("   💡 Ajoutez GEMINI_API_KEY=votre-cle-ici dans votre fichier .env")
        return False
    elif not api_key.startswith('AIza'):
        print(f"   ⚠️  Format de clé suspect: {api_key[:10]}...")
        print("   💡 Les clés Gemini commencent généralement par 'AIza'")
    else:
        print(f"   ✅ Clé API trouvée: {api_key[:10]}...{api_key[-4:]}")
    
    # 3. Tester l'import de la bibliothèque
    print(f"\n2. Import de la bibliothèque:")
    try:
        import google.generativeai as genai
        print("   ✅ google-generativeai importé avec succès")
    except ImportError as e:
        print(f"   ❌ Erreur d'import: {e}")
        print("   💡 Installez avec: pip install google-generativeai")
        return False
    
    # 4. Configurer l'API
    print(f"\n3. Configuration de l'API:")
    try:
        genai.configure(api_key=api_key)
        print("   ✅ API configurée")
    except Exception as e:
        print(f"   ❌ Erreur de configuration: {e}")
        return False
    
    # 5. Tester la connexion
    print(f"\n4. Test de connexion:")
    try:
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Dis simplement 'test réussi'")
        
        if response.candidates and len(response.candidates) > 0:
            response_text = response.candidates[0].content.parts[0].text
            print(f"   ✅ Connexion réussie!")
            print(f"   📝 Réponse: {response_text}")
            return True
        else:
            print("   ❌ Aucune réponse du modèle")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")
        
        # Diagnostics supplémentaires
        if "API_KEY" in str(e):
            print("   💡 Problème de clé API - vérifiez qu'elle est valide")
        elif "quota" in str(e).lower():
            print("   💡 Quota dépassé - vérifiez vos limites sur Google Cloud Console")
        elif "permission" in str(e).lower():
            print("   💡 Problème de permissions - vérifiez que l'API Gemini est activée")
        
        return False

def test_flask_integration():
    """Teste l'intégration avec Flask"""
    print(f"\n" + "=" * 50)
    print("🔍 Test d'intégration Flask")
    print("=" * 50)
    
    try:
        # Importer le contexte Flask
        from app import create_app
        from app.modules.ai_support.gemini_service import gemini_service
        
        app = create_app()
        
        with app.app_context():
            print("1. Contexte Flask créé ✅")
            
            # Tester le service
            print("2. Test du service Gemini:")
            if gemini_service.is_available():
                print("   ✅ Service disponible")
                
                # Test de connexion
                result = gemini_service.test_connection()
                if result['success']:
                    print(f"   ✅ Test réussi: {result.get('response', 'N/A')}")
                else:
                    print(f"   ❌ Test échoué: {result.get('error', 'N/A')}")
            else:
                print("   ❌ Service non disponible")
                
    except Exception as e:
        print(f"❌ Erreur d'intégration Flask: {e}")

def main():
    """Fonction principale"""
    print("🤖 Diagnostic Gemini AI pour le système POS")
    print("=" * 60)
    
    # Test de base
    basic_success = test_gemini_configuration()
    
    if basic_success:
        print(f"\n✅ Configuration de base OK!")
        
        # Test d'intégration Flask
        test_flask_integration()
        
        print(f"\n🎉 Diagnostic terminé!")
        print(f"\n💡 Si le chat ne fonctionne toujours pas:")
        print(f"   1. Redémarrez l'application Flask")
        print(f"   2. Vérifiez les logs de l'application")
        print(f"   3. Utilisez le bouton 'Debug' dans le chat (admin)")
        
    else:
        print(f"\n❌ Configuration incomplète!")
        print(f"\n🔧 Actions requises:")
        print(f"   1. Obtenez une clé API sur https://makersuite.google.com/app/apikey")
        print(f"   2. Ajoutez GEMINI_API_KEY=votre-cle dans le fichier .env")
        print(f"   3. Relancez ce script pour vérifier")

if __name__ == "__main__":
    main()
