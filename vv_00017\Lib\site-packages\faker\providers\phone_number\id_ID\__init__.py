from .. import Provider as PhoneNumberProvider


class Provider(PhoneNumberProvider):
    # Currently this is my own work
    formats = (
        "+62-##-###-####",
        "+62-0##-###-####",
        "+62 (0##) ### ####",
        "+62 (0##) ###-####",
        "+62 (##) ### ####",
        "+62 (##) ###-####",
        "+62-###-###-####",
        "+62-0###-###-####",
        "+62 (0###) ### ####",
        "+62 (0###) ###-####",
        "+62 (###) ### ####",
        "+62 (###) ###-####",
        "(0##) ### ####",
        "(0##) ###-####",
        "(0###) ### ####",
        "(0###) ###-####",
        "08# ### ####",
        "08########",
    )
