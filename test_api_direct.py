#!/usr/bin/env python3
"""
Test direct de l'API de chat pour identifier le problème exact
"""

import requests
import json

def test_api_endpoints():
    """Teste différents endpoints de l'API"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Test des endpoints API")
    print("=" * 40)
    
    # 1. Test de l'endpoint simple
    print("1. Test de l'endpoint simple...")
    try:
        response = requests.get(f"{base_url}/support/api/test-simple")
        print(f"   Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Succès: {data}")
        elif response.status_code == 302:
            print("   🔄 Redirection (connexion requise)")
        else:
            print(f"   ❌ Erreur: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")
    
    # 2. Test de l'endpoint de chat de test
    print("\n2. Test de l'endpoint de chat de test...")
    try:
        test_data = {
            "message": "Test de l'API"
        }
        
        response = requests.post(
            f"{base_url}/support/api/chat/send-test",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Succès: {data.get('success', False)}")
            print(f"   📝 Message IA: {data.get('ai_message', {}).get('content', 'N/A')[:50]}...")
        elif response.status_code == 302:
            print("   🔄 Redirection (connexion requise)")
        elif response.status_code == 400:
            try:
                error_data = response.json()
                print(f"   ❌ Erreur 400: {error_data}")
            except:
                print(f"   ❌ Erreur 400: {response.text}")
        else:
            print(f"   ❌ Erreur {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")
    
    # 3. Test de l'endpoint de chat principal
    print("\n3. Test de l'endpoint de chat principal...")
    try:
        test_data = {
            "message": "Test du chat principal"
        }
        
        response = requests.post(
            f"{base_url}/support/api/chat/send",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Succès: {data.get('success', False)}")
        elif response.status_code == 302:
            print("   🔄 Redirection (connexion requise)")
        elif response.status_code == 400:
            try:
                error_data = response.json()
                print(f"   ❌ Erreur 400: {error_data}")
            except:
                print(f"   ❌ Erreur 400: {response.text}")
        else:
            print(f"   ❌ Erreur {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")

def main():
    """Fonction principale"""
    print("🚀 Test direct des APIs de chat")
    print("=" * 50)
    
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("💡 Instructions pour tester avec authentification:")
    print("1. Connectez-vous à l'interface web")
    print("2. Ouvrez la console du navigateur")
    print("3. Testez manuellement les endpoints")
    print("4. Vérifiez les logs de l'application Flask")
    
    print("\n🔧 Pour tester l'API de test:")
    print("Modifiez temporairement le JavaScript pour utiliser:")
    print("url: '/support/api/chat/send-test'")

if __name__ == "__main__":
    main()
