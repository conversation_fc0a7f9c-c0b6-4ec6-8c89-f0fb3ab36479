<!doctype html>
<html>
  <head>
    <title>CodeMirror: VBScript mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="vbscript.js"></script>
    <link rel="stylesheet" href="../../doc/docs.css">
    <style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
  </head>
  <body>
    <h1>CodeMirror: VBScript mode</h1>

<div><textarea id="code" name="code">
' <PERSON>
' 03-04-2012
'
' Basic VBScript support for codemirror2

Const ForReading = 1, ForWriting = 2, ForAppending = 8

Call Sub020_PostBroadcastToUrbanAirship(strUserName, strPassword, intTransmitID, strResponse)

If Not IsNull(strResponse) AND Len(strResponse) = 0 Then
	boolTransmitOkYN = False
Else
	' WScript.Echo "Oh Happy Day! Oh Happy DAY!"
	boolTransmitOkYN = True
End If
</textarea></div>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/vbscript</code>.</p>
  </body>
</html>

