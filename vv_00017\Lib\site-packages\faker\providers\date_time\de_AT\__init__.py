from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "<PERSON>nta<PERSON>",
        "1": "<PERSON><PERSON>",
        "2": "<PERSON><PERSON><PERSON>",
        "3": "<PERSON><PERSON><PERSON><PERSON>",
        "4": "<PERSON><PERSON><PERSON>",
        "5": "<PERSON><PERSON><PERSON>",
        "6": "Samstag",
    }

    MONTH_NAMES = {
        "01": "<PERSON><PERSON><PERSON>",
        "02": "<PERSON><PERSON><PERSON>",
        "03": "<PERSON><PERSON><PERSON>",
        "04": "April",
        "05": "<PERSON>",
        "06": "<PERSON><PERSON>",
        "07": "<PERSON><PERSON>",
        "08": "August",
        "09": "September",
        "10": "<PERSON><PERSON><PERSON>",
        "11": "November",
        "12": "Dezember",
    }

    def day_of_week(self):
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self):
        month = self.month()
        return self.MONTH_NAMES[month]
