from faker.typing import Country

from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "Воскресенье",
        "1": "Понедельник",
        "2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "3": "Среда",
        "4": "Четверг",
        "5": "Пятница",
        "6": "Суббота",
    }

    MONTH_NAMES = {
        "01": "Январь",
        "02": "Февраль",
        "03": "Март",
        "04": "Апрель",
        "05": "Май",
        "06": "Июнь",
        "07": "Июль",
        "08": "Август",
        "09": "Сентябрь",
        "10": "Октябрь",
        "11": "Ноябрь",
        "12": "Декабрь",
    }

    # Timezone names are based on Wiki list, source: https://ru.wikipedia.org/wiki/Список_часовых_поясов_по_странам
    countries = [
        Country(
            timezones=["Андорра (UTC+01)"],
            alpha_2_code="AD",
            alpha_3_code="AND",
            continent="Европа",
            name="Андорра",
            capital="Андорра-ла-Велья",
        ),
        Country(
            timezones=["Афганистан (UTC+04:30)"],
            alpha_2_code="AF",
            alpha_3_code="AFG",
            continent="Азия",
            name="Афганистан",
            capital="Кабул",
        ),
        Country(
            timezones=["Антигуа и Барбуда (UTC-04)"],
            alpha_2_code="AG",
            alpha_3_code="ATG",
            continent="Северная Америка",
            name="Антигуа и Барбуда",
            capital="Сент-Джонс",
        ),
        Country(
            timezones=["Албания (UTC+01)"],
            alpha_2_code="AL",
            alpha_3_code="ALB",
            continent="Европа",
            name="Албания",
            capital="Тирана",
        ),
        Country(
            timezones=["Армения (UTC+04)"],
            alpha_2_code="AM",
            alpha_3_code="ARM",
            continent="Азия",
            name="Армения",
            capital="Ереван",
        ),
        Country(
            timezones=["Ангола (UTC+01)"],
            alpha_2_code="AO",
            alpha_3_code="AGO",
            continent="Африка",
            name="Ангола",
            capital="Луанда",
        ),
        Country(
            timezones=["Аргентина (UTC-03)"],
            alpha_2_code="AR",
            alpha_3_code="ARG",
            continent="Южная Америка",
            name="Аргентина",
            capital="Буэнос Айрес",
        ),
        Country(
            timezones=["Австрия (UTC+01)"],
            alpha_2_code="AT",
            alpha_3_code="AUT",
            continent="Европа",
            name="Австрия",
            capital="Вена",
        ),
        Country(
            timezones=[
                "Австралия (UTC+05)",
                "Австралия (UTC+06:30)",
                "Австралия (UTC+07)",
                "Австралия (UTC+08)",
                "Австралия (UTC+9:30)",
                "Австралия (UTC+10)",
                "Австралия (UTC+10:30)",
                "Австралия (UTC+11:30)",
            ],
            alpha_2_code="AU",
            alpha_3_code="AUS",
            continent="Океания",
            name="Австралия",
            capital="Канберра",
        ),
        Country(
            timezones=["Азербайджан (UTC+04)"],
            alpha_2_code="AZ",
            alpha_3_code="AZE",
            continent="Азия",
            name="Азербайджан",
            capital="Баку",
        ),
        Country(
            timezones=["Барбадос (UTC-04)"],
            alpha_2_code="BB",
            alpha_3_code="BRB",
            continent="Северная Америка",
            name="Барбадос",
            capital="Бриджтаун",
        ),
        Country(
            timezones=["Бангладеш (UTC+06)"],
            alpha_2_code="BD",
            alpha_3_code="BGD",
            continent="Азия",
            name="Бангладеш",
            capital="Дака",
        ),
        Country(
            timezones=["Бельгия (UTC+01)"],
            alpha_2_code="BE",
            alpha_3_code="BEL",
            continent="Европа",
            name="Бельгия",
            capital="Брюссель",
        ),
        Country(
            timezones=["Буркина-Фасо (UTC)"],
            alpha_2_code="BF",
            alpha_3_code="BFA",
            continent="Африка",
            name="Буркина-Фасо",
            capital="Уагадугу",
        ),
        Country(
            timezones=["Болгария (UTC+02)"],
            alpha_2_code="BG",
            alpha_3_code="BGR",
            continent="Европа",
            name="Болгария",
            capital="София",
        ),
        Country(
            timezones=["Бахрейн (UTC+03)"],
            alpha_2_code="BH",
            alpha_3_code="BHR",
            continent="Азия",
            name="Бахрейн",
            capital="Манама",
        ),
        Country(
            timezones=["Бурунди (UTC+02)"],
            alpha_2_code="BI",
            alpha_3_code="BDI",
            continent="Африка",
            name="Бурунди",
            capital="Гитега",
        ),
        Country(
            timezones=["Бенин (UTC+01)"],
            alpha_2_code="BJ",
            alpha_3_code="BEN",
            continent="Африка",
            name="Бенин",
            capital="Порто-Ново",
        ),
        Country(
            timezones=["Бруней (UTC+08)"],
            alpha_2_code="BN",
            alpha_3_code="BRN",
            continent="Азия",
            name="Бруней",
            capital="Бандар-Сери-Бегаван",
        ),
        Country(
            timezones=["Боливия (UTC-04)"],
            alpha_2_code="BO",
            alpha_3_code="BOL",
            continent="Южная Америка",
            name="Боливия",
            capital="Сукре",
        ),
        Country(
            timezones=[
                "Бразилия (UTC-05)",
                "Бразилия (UTC-04)",
                "Бразилия (UTC-03)",
                "Бразилия (UTC-02)",
            ],
            alpha_2_code="BR",
            alpha_3_code="BRA",
            continent="Южная Америка",
            name="Бразилия",
            capital="Бразилиа",
        ),
        Country(
            timezones=["Багамские Острова (UTC-05)"],
            alpha_2_code="BS",
            alpha_3_code="BHS",
            continent="Северная Америка",
            name="Багамские Острова",
            capital="Нассау",
        ),
        Country(
            timezones=["Бутан (UTC+06)"],
            alpha_2_code="BT",
            alpha_3_code="BTN",
            continent="Азия",
            name="Бутан",
            capital="Тхимпху",
        ),
        Country(
            timezones=["Ботсвана (UTC+02)"],
            alpha_2_code="BW",
            alpha_3_code="BWA",
            continent="Африка",
            name="Ботсвана",
            capital="Габороне",
        ),
        Country(
            timezones=["Белоруссия (UTC+03)"],
            alpha_2_code="BY",
            alpha_3_code="BLR",
            continent="Европа",
            name="Белоруссия",
            capital="Минск",
        ),
        Country(
            timezones=["Белиз (UTC-06)"],
            alpha_2_code="BZ",
            alpha_3_code="BLZ",
            continent="Северная Америка",
            name="Белиз",
            capital="Бельмопан",
        ),
        Country(
            timezones=[
                "Канада (UTC-08)",
                "Канада (UTC-07)",
                "Канада (UTC-06)",
                "Канада (UTC-05)",
                "Канада (UTC-04)",
                "Канада (UTC-03:30)",
            ],
            alpha_2_code="CA",
            alpha_3_code="CAN",
            continent="Северная Америка",
            name="Канада",
            capital="Оттава",
        ),
        Country(
            timezones=[
                "Демократическая Республика Конго (UTC+01)",
                "Демократическая Республика Конго (UTC+02)",
            ],
            alpha_2_code="CD",
            alpha_3_code="COD",
            continent="Африка",
            name="Демократическая Республика Конго",
            capital="Киншаса",
        ),
        Country(
            timezones=["Республика Конго (UTC+01)"],
            alpha_2_code="CG",
            alpha_3_code="COG",
            continent="Африка",
            name="Руспублика Конго",
            capital="Браззавиль",
        ),
        Country(
            timezones=["Кот-д'Ивуар (UTC)"],
            alpha_2_code="CI",
            alpha_3_code="CIV",
            continent="Африка",
            name="Кот-д'Ивуар",
            capital="Ямусукро",
        ),
        Country(
            timezones=["Чили (UTC-06)", "Чили (UTC-04)"],
            alpha_2_code="CL",
            alpha_3_code="CHL",
            continent="Южная Америка",
            name="Чили",
            capital="Сантьяго",
        ),
        Country(
            timezones=["Камерун (UTC+01)"],
            alpha_2_code="CM",
            alpha_3_code="CMR",
            continent="Африка",
            name="Камерун",
            capital="Яунде",
        ),
        Country(
            timezones=["Китай (UTC+08)"],
            alpha_2_code="CN",
            alpha_3_code="CHN",
            continent="Азия",
            name="Китайская Народная Республика",
            capital="Пекин",
        ),
        Country(
            timezones=["Колумбия (UTC-05)"],
            alpha_2_code="CO",
            alpha_3_code="COL",
            continent="Южная Америка",
            name="Колумбия",
            capital="Богота",
        ),
        Country(
            timezones=["Коста-Рика (UTC-06)"],
            alpha_2_code="CR",
            alpha_3_code="CRI",
            continent="Северная Америка",
            name="Коста-Рика",
            capital="Сан-Хосе",
        ),
        Country(
            timezones=["Куба (UTC-05)"],
            alpha_2_code="CU",
            alpha_3_code="CUB",
            continent="Северная Америка",
            name="Куба",
            capital="Гавана",
        ),
        Country(
            timezones=["Кабо-Верде (UTC-01)"],
            alpha_2_code="CV",
            alpha_3_code="CPV",
            continent="Африка",
            name="Кабо-Верде",
            capital="Прая",
        ),
        Country(
            timezones=["Кипр (UTC+02)"],
            alpha_2_code="CY",
            alpha_3_code="CYP",
            continent="Азия",
            name="Кипр",
            capital="Никосия",
        ),
        Country(
            timezones=["Чехия (UTC+01)"],
            alpha_2_code="CZ",
            alpha_3_code="CZE",
            continent="Европа",
            name="Чехия",
            capital="Прага",
        ),
        Country(
            timezones=["Германия (UTC+01)"],
            alpha_2_code="DE",
            alpha_3_code="DEU",
            continent="Европа",
            name="Германия",
            capital="Берлин",
        ),
        Country(
            timezones=["Джибути (UTC+03)"],
            alpha_2_code="DJ",
            alpha_3_code="DJI",
            continent="Африка",
            name="Джибути",
            capital="Джибути",
        ),
        Country(
            timezones=["Дания (UTC+01)"],
            alpha_2_code="DK",
            alpha_3_code="DNK",
            continent="Европа",
            name="Дания",
            capital="Копенгаген",
        ),
        Country(
            timezones=["Доминика (UTC-04)"],
            alpha_2_code="DM",
            alpha_3_code="DMA",
            continent="Северная Америка",
            name="Доминика",
            capital="Розо",
        ),
        Country(
            timezones=["Доминиканская Республика (UTC-04)"],
            alpha_2_code="DO",
            alpha_3_code="DOM",
            continent="Северная Америка",
            name="Доминиканская Республика",
            capital="Санто-Доминго",
        ),
        Country(
            timezones=["Эквадор (UTC-06)", "Эквадор (UTC-05)"],
            alpha_2_code="EC",
            alpha_3_code="ECU",
            continent="Южная Америка",
            name="Эквадор",
            capital="Кито",
        ),
        Country(
            timezones=["Эстония (UTC+02)"],
            alpha_2_code="EE",
            alpha_3_code="EST",
            continent="Европа",
            name="Эстония",
            capital="Таллинн",
        ),
        Country(
            timezones=["Египет (UTC+02)"],
            alpha_2_code="EG",
            alpha_3_code="EGY",
            continent="Африка",
            name="Египет",
            capital="Каир",
        ),
        Country(
            timezones=["Эритрея (UTC+03)"],
            alpha_2_code="ER",
            alpha_3_code="ERI",
            continent="Африка",
            name="Эритрея",
            capital="Асмэра",
        ),
        Country(
            timezones=["Эфиопия (UTC+03)"],
            alpha_2_code="ET",
            alpha_3_code="ETH",
            continent="Африка",
            name="Эфиопия",
            capital="Аддис-Абеба",
        ),
        Country(
            timezones=["Финляндия (UTC+02)"],
            alpha_2_code="FI",
            alpha_3_code="FIN",
            continent="Европа",
            name="Финляндия",
            capital="Хельсинки",
        ),
        Country(
            timezones=["Фиджи (UTC+12)"],
            alpha_2_code="FJ",
            alpha_3_code="FJI",
            continent="Океания",
            name="Фиджи",
            capital="Сува",
        ),
        Country(
            timezones=["Франция (UTC+01)"],
            alpha_2_code="FR",
            alpha_3_code="FRA",
            continent="Европа",
            name="Франция",
            capital="Париж",
        ),
        Country(
            timezones=["Габон (UTC+01)"],
            alpha_2_code="GA",
            alpha_3_code="GAB",
            continent="Африка",
            name="Габон",
            capital="Либревиль",
        ),
        Country(
            timezones=["Грузия (UTC+04)"],
            alpha_2_code="GE",
            alpha_3_code="GEO",
            continent="Азия",
            name="Грузия",
            capital="Тбилиси",
        ),
        Country(
            timezones=["Гана (UTC)"],
            alpha_2_code="GH",
            alpha_3_code="GHA",
            continent="Африка",
            name="Гана",
            capital="Аккра",
        ),
        Country(
            timezones=["Гамбия (UTC)"],
            alpha_2_code="GM",
            alpha_3_code="GMB",
            continent="Африка",
            name="Гамбия",
            capital="Банджул",
        ),
        Country(
            timezones=["Гвинея (UTC)"],
            alpha_2_code="GN",
            alpha_3_code="GIN",
            continent="Африка",
            name="Гвинея",
            capital="Конакри",
        ),
        Country(
            timezones=["Греция (UTC+02)"],
            alpha_2_code="GR",
            alpha_3_code="GRC",
            continent="Европа",
            name="Греция",
            capital="Афины",
        ),
        Country(
            timezones=["Гватемала (UTC-06)"],
            alpha_2_code="GT",
            alpha_3_code="GTM",
            continent="Северная Америка",
            name="Гватемала",
            capital="Гватемала",
        ),
        Country(
            timezones=["Гаити (UTC-05)"],
            alpha_2_code="HT",
            alpha_3_code="HTI",
            continent="Северная Америка",
            name="Гаити",
            capital="Порт-о-Пренс",
        ),
        Country(
            timezones=["Гвинея-Бисау (UTC)"],
            alpha_2_code="GW",
            alpha_3_code="GNB",
            continent="Африка",
            name="Гвинея-Бисау",
            capital="Бисау",
        ),
        Country(
            timezones=["Гайана (UTC-04)"],
            alpha_2_code="GY",
            alpha_3_code="GUY",
            continent="Южная Америка",
            name="Гайана",
            capital="Джорджтаун",
        ),
        Country(
            timezones=["Гондурас (UTC-06)"],
            alpha_2_code="HN",
            alpha_3_code="HND",
            continent="Северная Америка",
            name="Гондурас",
            capital="Тегусигальпа",
        ),
        Country(
            timezones=["Венгрия (UTC+01)"],
            alpha_2_code="HU",
            alpha_3_code="HUN",
            continent="Европа",
            name="Венгрия",
            capital="Будапешт",
        ),
        Country(
            timezones=[
                "Индонезия (UTC+07)",
                "Индонезия (UTC+08)",
                "Индонезия (UTC+09)",
            ],
            alpha_2_code="ID",
            alpha_3_code="IDN",
            continent="Азия",
            name="Индонезия",
            capital="Джакарта",
        ),
        Country(
            timezones=["Ирландия (UTC)"],
            alpha_2_code="IE",
            alpha_3_code="IRL",
            continent="Европа",
            name="Ирландия",
            capital="Дублин",
        ),
        Country(
            timezones=["Израиль (UTC+02)"],
            alpha_2_code="IL",
            alpha_3_code="ISR",
            continent="Азия",
            name="Израиль",
            capital="Иерусалим",
        ),
        Country(
            timezones=["Индия (UTC+05:30"],
            alpha_2_code="IN",
            alpha_3_code="IND",
            continent="Азия",
            name="Индия",
            capital="Дели",
        ),
        Country(
            timezones=["Ирак (UTC+03)"],
            alpha_2_code="IQ",
            alpha_3_code="IRQ",
            continent="Азия",
            name="Ирак",
            capital="Багдад",
        ),
        Country(
            timezones=["Иран (UTC+03:30)"],
            alpha_2_code="IR",
            alpha_3_code="IRN",
            continent="Азия",
            name="Иран",
            capital="Тегеран",
        ),
        Country(
            timezones=["Исландия (UTC)"],
            alpha_2_code="IS",
            alpha_3_code="ISL",
            continent="Европа",
            name="Исландия",
            capital="Рейкьявик",
        ),
        Country(
            timezones=["Италия (UTC+01)"],
            alpha_2_code="IT",
            alpha_3_code="ITA",
            continent="Европа",
            name="Италия",
            capital="Рим",
        ),
        Country(
            timezones=["Ямайка (UTC-05)"],
            alpha_2_code="JM",
            alpha_3_code="JAM",
            continent="Северная Америка",
            name="Ямайка",
            capital="Кингстон",
        ),
        Country(
            timezones=["Иордания (UTC+02)"],
            alpha_2_code="JO",
            alpha_3_code="JOR",
            continent="Азия",
            name="Иордания",
            capital="Амман",
        ),
        Country(
            timezones=["Япония (UTC+09)"],
            alpha_2_code="JP",
            alpha_3_code="JPN",
            continent="Азия",
            name="Япония",
            capital="Токио",
        ),
        Country(
            timezones=["Кения (UTC+03)"],
            alpha_2_code="KE",
            alpha_3_code="KEN",
            continent="Африка",
            name="Кения",
            capital="Найроби",
        ),
        Country(
            timezones=["Киргизия (UTC+06)"],
            alpha_2_code="KG",
            alpha_3_code="KGZ",
            continent="Азия",
            name="Киргизия",
            capital="Бишкек",
        ),
        Country(
            timezones=[
                "Кирибати (UTC+12)",
                "Кирибати (UTC+13)",
                "Кирибати (UTC+14)",
            ],
            alpha_2_code="KI",
            alpha_3_code="KIR",
            continent="Океания",
            name="Кирибати",
            capital="Южная Тарава",
        ),
        Country(
            timezones=["КНДР (UTC+09)"],
            alpha_2_code="KP",
            alpha_3_code="PRK",
            continent="Азия",
            name="КНДР",
            capital="Пхеньян",
        ),
        Country(
            timezones=["Республика Корея (UTC+09)"],
            alpha_2_code="KR",
            alpha_3_code="KOR",
            continent="Азия",
            name="Республика Корея",
            capital="Сеул",
        ),
        Country(
            timezones=["Кувейт (UTC+03)"],
            alpha_2_code="KW",
            alpha_3_code="KWT",
            continent="Азия",
            name="Кувейт",
            capital="Эль-Кувейт",
        ),
        Country(
            timezones=["Ливан (UTC+02)"],
            alpha_2_code="LB",
            alpha_3_code="LBN",
            continent="Азия",
            name="Ливан",
            capital="Бейрут",
        ),
        Country(
            timezones=["Лихтенштейн (UTC+01)"],
            alpha_2_code="LI",
            alpha_3_code="LIE",
            continent="Европа",
            name="Лихтенштейн",
            capital="Вадуц",
        ),
        Country(
            timezones=["Либерия (UTC)"],
            alpha_2_code="LR",
            alpha_3_code="LBR",
            continent="Африка",
            name="Либерия",
            capital="Монровия",
        ),
        Country(
            timezones=["Лесото (UTC+02)"],
            alpha_2_code="LS",
            alpha_3_code="LSO",
            continent="Африка",
            name="Лесото",
            capital="Масеру",
        ),
        Country(
            timezones=["Литва (UTC+02)"],
            alpha_2_code="LT",
            alpha_3_code="LTU",
            continent="Европа",
            name="Литва",
            capital="Вильнюс",
        ),
        Country(
            timezones=["Люксембург (UTC+01)"],
            alpha_2_code="LU",
            alpha_3_code="LUX",
            continent="Европа",
            name="Люксембург",
            capital="Люксембург",
        ),
        Country(
            timezones=["Латвия (UTC+02)"],
            alpha_2_code="LV",
            alpha_3_code="LVA",
            continent="Европа",
            name="Латвия",
            capital="Рига",
        ),
        Country(
            timezones=["Ливия (UTC+02)"],
            alpha_2_code="LY",
            alpha_3_code="LBY",
            continent="Африка",
            name="Ливия",
            capital="Триполи",
        ),
        Country(
            timezones=["Мадагаскар (UTC+03)"],
            alpha_2_code="MG",
            alpha_3_code="MDG",
            continent="Африка",
            name="Мадагаскар",
            capital="Антананариву",
        ),
        Country(
            timezones=["Маршалловы Острова (UTC+12)"],
            alpha_2_code="MH",
            alpha_3_code="MHL",
            continent="Океания",
            name="Маршалловы Острова",
            capital="Маджуро",
        ),
        Country(
            timezones=["Северная Македония (UTC+01)"],
            alpha_2_code="MK",
            alpha_3_code="MKD",
            continent="Европа",
            name="Северная Македония",
            capital="Скопье",
        ),
        Country(
            timezones=["Мали (UTC)"],
            alpha_2_code="ML",
            alpha_3_code="MLI",
            continent="Африка",
            name="Мали",
            capital="Бамако",
        ),
        Country(
            timezones=["Мьянма (UTC+06:30)"],
            alpha_2_code="MM",
            alpha_3_code="MMR",
            continent="Азия",
            name="Мьянма",
            capital="Нейпьидо",
        ),
        Country(
            timezones=["Монголия (UTC+07)", "Монголия (UTC+08)"],
            alpha_2_code="MN",
            alpha_3_code="MNG",
            continent="Азия",
            name="Монголия",
            capital="Улан-Батор",
        ),
        Country(
            timezones=["Мавритания (UTC)"],
            alpha_2_code="MR",
            alpha_3_code="MRT",
            continent="Африка",
            name="Мавритания",
            capital="Нуакшот",
        ),
        Country(
            timezones=["Мальта (UTC+01)"],
            alpha_2_code="MT",
            alpha_3_code="MLT",
            continent="Европа",
            name="Мальта",
            capital="Валлетта",
        ),
        Country(
            timezones=["Маврикий (UTC+04)"],
            alpha_2_code="MU",
            alpha_3_code="MUS",
            continent="Африка",
            name="Маврикий",
            capital="Порт-Луи",
        ),
        Country(
            timezones=["Мальдивы (UTC+05)"],
            alpha_2_code="MV",
            alpha_3_code="MDV",
            continent="Азия",
            name="Мальдивы",
            capital="Мале",
        ),
        Country(
            timezones=["Малави (UTC+02)"],
            alpha_2_code="MW",
            alpha_3_code="MWI",
            continent="Африка",
            name="Малави",
            capital="Лилонгве",
        ),
        Country(
            timezones=["Мексика (UTC-08)", "Мексика (UTC-07)", "Мексика (UTC-06)"],
            alpha_2_code="MX",
            alpha_3_code="MEX",
            continent="Северная Америка",
            name="Мексика",
            capital="Мехико",
        ),
        Country(
            timezones=["Малайзия (UTC+08)"],
            alpha_2_code="MY",
            alpha_3_code="MYS",
            continent="Азия",
            name="Малайзия",
            capital="Куала-Лумпур",
        ),
        Country(
            timezones=["Мозамбик (UTC+02)"],
            alpha_2_code="MZ",
            alpha_3_code="MOZ",
            continent="Африка",
            name="Мозамбик",
            capital="Мапуту",
        ),
        Country(
            timezones=["Намибия (UTC+01)"],
            alpha_2_code="NA",
            alpha_3_code="NAM",
            continent="Африка",
            name="Намибия",
            capital="Виндхук",
        ),
        Country(
            timezones=["Нигер (UTC+01)"],
            alpha_2_code="NE",
            alpha_3_code="NER",
            continent="Африка",
            name="Нигер",
            capital="Ниамей",
        ),
        Country(
            timezones=["Нигерия (UTC+01)"],
            alpha_2_code="NG",
            alpha_3_code="NGA",
            continent="Африка",
            name="Нигерия",
            capital="Абуджа",
        ),
        Country(
            timezones=["Никарагуа (UTC-06)"],
            alpha_2_code="NI",
            alpha_3_code="NIC",
            continent="Северная Америка",
            name="Никарагуа",
            capital="Манагуа",
        ),
        Country(
            timezones=["Нидерланды (UTC+01)"],
            alpha_2_code="NL",
            alpha_3_code="NLD",
            continent="Европа",
            name="Нидерланды",
            capital="Амстердам",
        ),
        Country(
            timezones=["Норвегия (UTC+01)"],
            alpha_2_code="NO",
            alpha_3_code="NOR",
            continent="Европа",
            name="Норвегия",
            capital="Осло",
        ),
        Country(
            timezones=["Непал (UTC+05:45"],
            alpha_2_code="NP",
            alpha_3_code="NPL",
            continent="Азия",
            name="Непал",
            capital="Катманду",
        ),
        Country(
            timezones=["Науру (UTC+12)"],
            alpha_2_code="NR",
            alpha_3_code="NRU",
            continent="Океания",
            name="Науру",
            capital="Ярен",
        ),
        Country(
            timezones=["Новая Зеландия (UTC+12)"],
            alpha_2_code="NZ",
            alpha_3_code="NZL",
            continent="Океания",
            name="Новая Зеландия",
            capital="Веллингтон",
        ),
        Country(
            timezones=["Оман (UTC+04"],
            alpha_2_code="OM",
            alpha_3_code="OMN",
            continent="Азия",
            name="Оман",
            capital="Маскат",
        ),
        Country(
            timezones=["Панама (UTC-05)"],
            alpha_2_code="PA",
            alpha_3_code="PAN",
            continent="Северная Америка",
            name="Панама",
            capital="Панама",
        ),
        Country(
            timezones=["Перу (UTC-05)"],
            alpha_2_code="PE",
            alpha_3_code="PER",
            continent="Южная Америка",
            name="Перу",
            capital="Лима",
        ),
        Country(
            timezones=["Папуа - Новая Гвинея (UTC+10)"],
            alpha_2_code="PG",
            alpha_3_code="PNG",
            continent="Океания",
            name="Папуа - Новая Гвинея",
            capital="Порт-Морсби",
        ),
        Country(
            timezones=["Филиппины (UTC+08)"],
            alpha_2_code="PH",
            alpha_3_code="PHL",
            continent="Азия",
            name="Филиппины",
            capital="Манила",
        ),
        Country(
            timezones=["Пакистан (UTC+05)"],
            alpha_2_code="PK",
            alpha_3_code="PAK",
            continent="Азия",
            name="Пакистан",
            capital="Исламабад",
        ),
        Country(
            timezones=["Польша (UTC+01)"],
            alpha_2_code="PL",
            alpha_3_code="POL",
            continent="Европа",
            name="Польша",
            capital="Варшава",
        ),
        Country(
            timezones=["Португалия (UTC)"],
            alpha_2_code="PT",
            alpha_3_code="PRT",
            continent="Европа",
            name="Португалия",
            capital="Лиссабон",
        ),
        Country(
            timezones=["Палау (UTC+09)"],
            alpha_2_code="PW",
            alpha_3_code="PLW",
            continent="Океания",
            name="Палау",
            capital="Кампала",
        ),
        Country(
            timezones=["Парагвай (UTC-04)"],
            alpha_2_code="PY",
            alpha_3_code="PRY",
            continent="Южная Америка",
            name="Парагвай",
            capital="Асунсьон",
        ),
        Country(
            timezones=["Катар (UTC+03)"],
            alpha_2_code="QA",
            alpha_3_code="QAT",
            continent="Азия",
            name="Катар",
            capital="Доха",
        ),
        Country(
            timezones=["Румыния (UTC+02)"],
            alpha_2_code="RO",
            alpha_3_code="ROU",
            continent="Европа",
            name="Румыния",
            capital="Бухарест",
        ),
        Country(
            timezones=[
                "Россия (UTC+02)",
                "Россия (UTC+03)",
                "Россия (UTC+04)",
                "Россия (UTC+05)",
                "Россия (UTC+06)",
                "Россия (UTC+07)",
                "Россия (UTC+08)",
                "Россия (UTC+09)",
                "Россия (UTC+10)",
                "Россия (UTC+11)",
                "Россия (UTC+12)",
            ],
            alpha_2_code="RU",
            alpha_3_code="RUS",
            continent="Европа",
            name="Россия",
            capital="Москва",
        ),
        Country(
            timezones=["Руанда (UTC+02)"],
            alpha_2_code="RW",
            alpha_3_code="RWA",
            continent="Африка",
            name="Руанда",
            capital="Кигали",
        ),
        Country(
            timezones=["Саудовская Аравия (UTC+03)"],
            alpha_2_code="SA",
            alpha_3_code="SAU",
            continent="Азия",
            name="Саудовская Аравия",
            capital="Эр-Рияд",
        ),
        Country(
            timezones=["Соломоновы Острова (UTC+11)"],
            alpha_2_code="SB",
            alpha_3_code="SLB",
            continent="Океания",
            name="Соломоновы Острова",
            capital="Хониара",
        ),
        Country(
            timezones=["Сейшельские острова (UTC+04)"],
            alpha_2_code="SC",
            alpha_3_code="SYC",
            continent="Африка",
            name="Сейшельские острова",
            capital="Виктория",
        ),
        Country(
            timezones=["Судан (UTC+03)"],
            alpha_2_code="SD",
            alpha_3_code="SDN",
            continent="Африка",
            name="Судан",
            capital="Хартум",
        ),
        Country(
            timezones=["Швеция (UTC+01)"],
            alpha_2_code="SE",
            alpha_3_code="SWE",
            continent="Европа",
            name="Швеци",
            capital="Стокгольм",
        ),
        Country(
            timezones=["Сингапур (UTC+08)"],
            alpha_2_code="SG",
            alpha_3_code="SGP",
            continent="Азия",
            name="Сингапур",
            capital="Сингапур",
        ),
        Country(
            timezones=["Словения (UTC+01)"],
            alpha_2_code="SI",
            alpha_3_code="SVN",
            continent="Европа",
            name="Словения",
            capital="Любляна",
        ),
        Country(
            timezones=["Словакия (UTC+01)"],
            alpha_2_code="SK",
            alpha_3_code="SVK",
            continent="Европа",
            name="Словакия",
            capital="Братислава",
        ),
        Country(
            timezones=["Сьерра-Леоне (UTC)"],
            alpha_2_code="SL",
            alpha_3_code="SLE",
            continent="Африка",
            name="Сьерра Леоне",
            capital="Фритаун",
        ),
        Country(
            timezones=["Сан-Марино (UTC+01)"],
            alpha_2_code="SM",
            alpha_3_code="SMR",
            continent="Европа",
            name="Сан-Марино",
            capital="Сан-Марино",
        ),
        Country(
            timezones=["Сенегал (UTC)"],
            alpha_2_code="SN",
            alpha_3_code="SEN",
            continent="Африка",
            name="Сенегал",
            capital="Дакар",
        ),
        Country(
            timezones=["Сомали (UTC+03)"],
            alpha_2_code="SO",
            alpha_3_code="SOM",
            continent="Африка",
            name="Сомали",
            capital="Могадишо",
        ),
        Country(
            timezones=["Суринам (UTC-03)"],
            alpha_2_code="SR",
            alpha_3_code="SUR",
            continent="Южная Америка",
            name="Суринам",
            capital="Парамарибо",
        ),
        Country(
            timezones=["Сан-Томе и Принсипи (UTC)"],
            alpha_2_code="ST",
            alpha_3_code="STP",
            continent="Африка",
            name="Сан-Томе и Принсипи",
            capital="Сан-Томе",
        ),
        Country(
            timezones=["Сирия (UTC+02)"],
            alpha_2_code="SY",
            alpha_3_code="SYR",
            continent="Азия",
            name="Сирия",
            capital="Дамаск",
        ),
        Country(
            timezones=["Того (UTC)"],
            alpha_2_code="TG",
            alpha_3_code="TGO",
            continent="Африка",
            name="Того",
            capital="Ломе",
        ),
        Country(
            timezones=["Таиланд (UTC+07)"],
            alpha_2_code="TH",
            alpha_3_code="THA",
            continent="Азия",
            name="Таиланд",
            capital="Бангкок",
        ),
        Country(
            timezones=["Таджикистан (UTC+05)"],
            alpha_2_code="TJ",
            alpha_3_code="TJK",
            continent="Азия",
            name="Таджикистан",
            capital="Душанбе",
        ),
        Country(
            timezones=["Туркмения (UTC+05)"],
            alpha_2_code="TM",
            alpha_3_code="TKM",
            continent="Азия",
            name="Туркмения",
            capital="Ашхабад",
        ),
        Country(
            timezones=["Тунис (UTC+01)"],
            alpha_2_code="TN",
            alpha_3_code="TUN",
            continent="Африка",
            name="Тунис",
            capital="Тунис",
        ),
        Country(
            timezones=["Тонга (UTC+13)"],
            alpha_2_code="TO",
            alpha_3_code="TON",
            continent="Океания",
            name="Тонга",
            capital="Нукуалофа",
        ),
        Country(
            timezones=["Турция (UTC+02)"],
            alpha_2_code="TR",
            alpha_3_code="TUR",
            continent="Азия",
            name="Турция",
            capital="Анкара",
        ),
        Country(
            timezones=["Тринидад и Тобаго (UTC-04)"],
            alpha_2_code="TT",
            alpha_3_code="TTO",
            continent="Северная Америка",
            name="Тринидад и Тобаго",
            capital="Порт-оф-Спейн",
        ),
        Country(
            timezones=["Тувалу (UTC+12)"],
            alpha_2_code="TV",
            alpha_3_code="TUV",
            continent="Океания",
            name="Тувалу",
            capital="Фунафути",
        ),
        Country(
            timezones=["Танзания (UTC+03)"],
            alpha_2_code="TZ",
            alpha_3_code="TZA",
            continent="Африка",
            name="Танзания",
            capital="Додома",
        ),
        Country(
            timezones=["Украина (UTC+02)", "Украина (UTC+03)"],
            alpha_2_code="UA",
            alpha_3_code="UKR",
            continent="Европа",
            name="Украина",
            capital="Киев",
        ),
        Country(
            timezones=["Уганда (UTC+03)"],
            alpha_2_code="UG",
            alpha_3_code="UGA",
            continent="Африка",
            name="Уганда",
            capital="Кампала",
        ),
        Country(
            timezones=[
                "США (UTC-11)",
                "США (UTC-10)",
                "США (UTC-09)",
                "США (UTC-08)",
                "США (UTC-07)",
                "США (UTC-06)",
                "США (UTC-05)",
                "США (UTC-04)",
                "США (UTC+10)",
            ],
            alpha_2_code="US",
            alpha_3_code="USA",
            continent="Северная Америка",
            name="США",
            capital="Вашингтон",
        ),
        Country(
            timezones=["Уругвай (UTC-03)"],
            alpha_2_code="UY",
            alpha_3_code="URY",
            continent="Южная Америка",
            name="Уругвай",
            capital="Монтевидео",
        ),
        Country(
            timezones=["Узбекистан (UTC+05)"],
            alpha_2_code="UZ",
            alpha_3_code="UZB",
            continent="Азия",
            name="Узбекистан",
            capital="Ташкент",
        ),
        Country(
            timezones=["Ватикан (UTC+01)"],
            alpha_2_code="VA",
            alpha_3_code="VAT",
            continent="Европа",
            name="Ватикан",
            capital="Ватикан",
        ),
        Country(
            timezones=["Венесуэла (UTC-04:30)"],
            alpha_2_code="VE",
            alpha_3_code="VEN",
            continent="Южная Америка",
            name="Венесуэла",
            capital="Каракас",
        ),
        Country(
            timezones=["Вьетнам (UTC+07)"],
            alpha_2_code="VN",
            alpha_3_code="VNM",
            continent="Азия",
            name="Вьетнам",
            capital="Ханой",
        ),
        Country(
            timezones=["Вануату (UTC+11)"],
            alpha_2_code="VU",
            alpha_3_code="VUT",
            continent="Океания",
            name="Вануату",
            capital="Порт-Вила",
        ),
        Country(
            timezones=["Йемен (UTC+03)"],
            alpha_2_code="YE",
            alpha_3_code="YEM",
            continent="Азия",
            name="Йемен",
            capital="Сана",
        ),
        Country(
            timezones=["Замбия (UTC+02)"],
            alpha_2_code="ZM",
            alpha_3_code="ZMB",
            continent="Африка",
            name="Замбия",
            capital="Лусака",
        ),
        Country(
            timezones=["Зимбабве (UTC+02)"],
            alpha_2_code="ZW",
            alpha_3_code="ZWE",
            continent="Африка",
            name="Зимбабве",
            capital="Хараре",
        ),
        Country(
            timezones=["Алжир (UTC+01)"],
            alpha_2_code="DZ",
            alpha_3_code="DZA",
            continent="Африка",
            name="Алжир",
            capital="Алжир",
        ),
        Country(
            timezones=["Босния и Герцеговина (UTC+01)"],
            alpha_2_code="BA",
            alpha_3_code="BIH",
            continent="Европа",
            name="Босния и Герцеговина",
            capital="Сараево",
        ),
        Country(
            timezones=["Камбоджа (UTC+07)"],
            alpha_2_code="KH",
            alpha_3_code="KHM",
            continent="Азия",
            name="Камбоджа",
            capital="Пномпень",
        ),
        Country(
            timezones=["ЦАР (UTC+01)"],
            alpha_2_code="CF",
            alpha_3_code="CAF",
            continent="Африка",
            name="ЦАР",
            capital="Банги",
        ),
        Country(
            timezones=["Чад (UTC+01)"],
            alpha_2_code="TD",
            alpha_3_code="TCD",
            continent="Африка",
            name="Чад",
            capital="Нджамена",
        ),
        Country(
            timezones=["Коморы (UTC+03)"],
            alpha_2_code="KM",
            alpha_3_code="COM",
            continent="Африка",
            name="Коморы",
            capital="Морони",
        ),
        Country(
            timezones=["Хорватия (UTC+01)"],
            alpha_2_code="HR",
            alpha_3_code="HRV",
            continent="Европа",
            name="Хорватия",
            capital="Загреб",
        ),
        Country(
            timezones=["Восточный Тимор (UTC+09)"],
            alpha_2_code="TL",
            alpha_3_code="TLS",
            continent="Азия",
            name="Восточный Тимор",
            capital="Дили",
        ),
        Country(
            timezones=["Сальвадор (UTC-06)"],
            alpha_2_code="SV",
            alpha_3_code="SLV",
            continent="Северная Америка",
            name="Сальвадор",
            capital="Сан-Сальвадор",
        ),
        Country(
            timezones=["Экваториальная Гвинея (UTC+01)"],
            alpha_2_code="GQ",
            alpha_3_code="GNQ",
            continent="Африка",
            name="Экваториальная Гвинея",
            capital="Малабо",
        ),
        Country(
            timezones=["Гренада (UTC-04)"],
            alpha_2_code="GD",
            alpha_3_code="GRD",
            continent="Северная Америка",
            name="Гренада",
            capital="Сент-Джорджес",
        ),
        Country(
            timezones=["Казахстан (UTC+05)", "Казахстан (UTC+06)"],
            alpha_2_code="KZ",
            alpha_3_code="KAZ",
            continent="Азия",
            name="Казахстан",
            capital="Нур-Султан (Астана)",
        ),
        Country(
            timezones=["Лаос (UTC+07)"],
            alpha_2_code="LA",
            alpha_3_code="LAO",
            continent="Азия",
            name="Лаос",
            capital="Вьентьян",
        ),
        Country(
            timezones=["Микронезия (UTC+10)", "Микронезия (UTC+11)"],
            alpha_2_code="FM",
            alpha_3_code="FSM",
            continent="Океания",
            name="Микронезия",
            capital="Паликир",
        ),
        Country(
            timezones=["Молдавия (UTC+02)"],
            alpha_2_code="MD",
            alpha_3_code="MDA",
            continent="Европа",
            name="Молдавия",
            capital="Кишинев",
        ),
        Country(
            timezones=["Монако (UTC+01)"],
            alpha_2_code="MC",
            alpha_3_code="MCO",
            continent="Европа",
            name="Монако",
            capital="Монако",
        ),
        Country(
            timezones=["Черногория (UTC+01)"],
            alpha_2_code="ME",
            alpha_3_code="MNE",
            continent="Европа",
            name="Черногория",
            capital="Подгорица",
        ),
        Country(
            timezones=["Марокко (UTC)"],
            alpha_2_code="MA",
            alpha_3_code="MAR",
            continent="Африка",
            name="Марокко",
            capital="Рабат",
        ),
        Country(
            timezones=["Сент-Китс и Невис (UTC-04)"],
            alpha_2_code="KN",
            alpha_3_code="KNA",
            continent="Северная Америка",
            name="Сент-Китс и Невис",
            capital="Бастер",
        ),
        Country(
            timezones=["Сент-Люсия (UTC-04)"],
            alpha_2_code="LC",
            alpha_3_code="LCA",
            continent="Северная Америка",
            name="Сент-Люсия",
            capital="Кастри",
        ),
        Country(
            timezones=["Сент-Винсент и Гренадины (UTC-04)"],
            alpha_2_code="VC",
            alpha_3_code="VCT",
            continent="Северная Америка",
            name="Сент-Винсент и Гренадины",
            capital="Кингстаун",
        ),
        Country(
            timezones=["Самоа (UTC+13)"],
            alpha_2_code="WS",
            alpha_3_code="WSM",
            continent="Океания",
            name="Самоа",
            capital="Апиа",
        ),
        Country(
            timezones=["Сербия (UTC+01)"],
            alpha_2_code="RS",
            alpha_3_code="SRB",
            continent="Европа",
            name="Сербия",
            capital="Белград",
        ),
        Country(
            timezones=["ЮАР (UTC+02)"],
            alpha_2_code="ZA",
            alpha_3_code="ZAF",
            continent="Африка",
            name="ЮАР",
            capital="Претория",
        ),
        Country(
            timezones=["Испания (UTC)", "Испания (UTC+01)"],
            alpha_2_code="ES",
            alpha_3_code="ESP",
            continent="Европа",
            name="Испания",
            capital="Мадрид",
        ),
        Country(
            timezones=["Шри-Ланка (UTC+05:30)"],
            alpha_2_code="LK",
            alpha_3_code="LKA",
            continent="Азия",
            name="Шри-Ланка",
            capital="Шри-Джаяварденепура-Котте",
        ),
        Country(
            timezones=["Эсватини (Свазиленд) (UTC+02)"],
            alpha_2_code="SZ",
            alpha_3_code="SWZ",
            continent="Африка",
            name="Эсватини (Свазиленд)",
            capital="Мбабане",
        ),
        Country(
            timezones=["Швейцария (UTC+01)"],
            alpha_2_code="CH",
            alpha_3_code="CHE",
            continent="Европа",
            name="Швейцария",
            capital="Берн",
        ),
        Country(
            timezones=["ОАЭ (UTC+04)"],
            alpha_2_code="AE",
            alpha_3_code="ARE",
            continent="Азия",
            name="ОАЭ",
            capital="Абу-Даби",
        ),
        Country(
            timezones=["Великобритания (UTC)"],
            alpha_2_code="GB",
            alpha_3_code="GBR",
            continent="Европа",
            name="Великобритания",
            capital="Лондон",
        ),
    ]

    def day_of_week(self) -> str:
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self) -> str:
        month = self.month()
        return self.MONTH_NAMES[month]
