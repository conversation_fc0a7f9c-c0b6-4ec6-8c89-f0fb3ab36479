"""
Service pour l'intégration avec l'API Gemini 2.0 Flash
"""

import google.generativeai as genai
import time
import logging
from typing import Dict, List, Optional, Tuple
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from flask import current_app
from datetime import datetime
import json

# Configuration du logging
logger = logging.getLogger(__name__)

class GeminiError(Exception):
    """Exception personnalisée pour les erreurs Gemini"""
    pass

class GeminiService:
    """Service pour interagir avec l'API Gemini 2.0 Flash"""
    
    def __init__(self):
        self.model = None
        self.is_configured = False
        self._configure()
    
    def _configure(self):
        """Configure le service Gemini avec les paramètres de l'application"""
        try:
            api_key = current_app.config.get('GEMINI_API_KEY')
            if not api_key:
                logger.warning("GEMINI_API_KEY non configurée. Le service AI sera désactivé.")
                return
            
            # Configuration de l'API
            genai.configure(api_key=api_key)
            
            # Configuration du modèle
            model_name = current_app.config.get('GEMINI_MODEL', 'gemini-2.0-flash-exp')
            
            generation_config = {
                "temperature": current_app.config.get('GEMINI_TEMPERATURE', 0.7),
                "top_p": current_app.config.get('GEMINI_TOP_P', 0.8),
                "top_k": current_app.config.get('GEMINI_TOP_K', 40),
                "max_output_tokens": current_app.config.get('GEMINI_MAX_TOKENS', 2048),
            }
            
            safety_settings = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
            ]
            
            self.model = genai.GenerativeModel(
                model_name=model_name,
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            self.is_configured = True
            logger.info(f"Service Gemini configuré avec le modèle {model_name}")
            
        except Exception as e:
            logger.error(f"Erreur lors de la configuration de Gemini: {str(e)}")
            self.is_configured = False
    
    def is_available(self) -> bool:
        """Vérifie si le service Gemini est disponible"""
        return self.is_configured and current_app.config.get('AI_SUPPORT_ENABLED', True)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((Exception,))
    )
    def generate_response(self, prompt: str, context: Optional[Dict] = None) -> Tuple[str, float]:
        """
        Génère une réponse avec Gemini
        
        Args:
            prompt: Le prompt à envoyer à Gemini
            context: Contexte additionnel (historique, informations utilisateur, etc.)
            
        Returns:
            Tuple[str, float]: (réponse, score de confiance)
        """
        if not self.is_available():
            raise GeminiError("Service Gemini non disponible")
        
        try:
            start_time = time.time()
            
            # Construire le prompt complet avec le contexte
            full_prompt = self._build_full_prompt(prompt, context)
            
            # Générer la réponse
            response = self.model.generate_content(full_prompt)
            
            processing_time = time.time() - start_time
            
            # Extraire la réponse
            if response.candidates and len(response.candidates) > 0:
                candidate = response.candidates[0]
                
                # Vérifier si la réponse a été bloquée pour des raisons de sécurité
                if candidate.finish_reason.name in ['SAFETY', 'RECITATION']:
                    logger.warning(f"Réponse bloquée: {candidate.finish_reason.name}")
                    return self._get_fallback_response(), 0.3
                
                response_text = candidate.content.parts[0].text
                
                # Calculer un score de confiance basé sur la qualité de la réponse
                confidence = self._calculate_confidence(response_text, candidate)
                
                logger.info(f"Réponse générée en {processing_time:.2f}s avec confiance {confidence:.2f}")
                
                return response_text, confidence
            else:
                logger.warning("Aucune réponse générée par Gemini")
                return self._get_fallback_response(), 0.2
                
        except Exception as e:
            logger.error(f"Erreur lors de la génération de réponse: {str(e)}")
            raise GeminiError(f"Erreur Gemini: {str(e)}")
    
    def _build_full_prompt(self, user_prompt: str, context: Optional[Dict] = None) -> str:
        """Construit le prompt complet avec le contexte du système POS"""
        
        system_context = """
Tu es un assistant IA spécialisé dans le support technique pour un système de point de vente (POS) moderne.

CONTEXTE DU SYSTÈME:
- Système POS complet avec gestion des ventes, inventaire, clients, rapports
- Interface web avec modules: caisse, inventaire, clients, promotions, tables, dépenses
- Base de données avec produits, catégories, recettes, fournisseurs
- Système de gestion des utilisateurs avec différents rôles
- Rapports et analytics intégrés
- Support multi-tables pour restaurants

TON RÔLE:
- Fournir une assistance technique précise et utile
- Expliquer les fonctionnalités du système de manière claire
- Aider à résoudre les problèmes techniques
- Guider les utilisateurs dans l'utilisation du système
- Être professionnel, courtois et patient

INSTRUCTIONS:
- Réponds en français
- Sois concis mais complet
- Propose des solutions pratiques
- Si tu ne connais pas la réponse, dis-le honnêtement
- Suggère de créer un ticket ou de contacter un agent si nécessaire
- Utilise des exemples concrets quand c'est utile
"""
        
        # Ajouter le contexte spécifique si fourni
        if context:
            if 'conversation_history' in context:
                system_context += f"\n\nHISTORIQUE DE LA CONVERSATION:\n{context['conversation_history']}"
            
            if 'user_info' in context:
                system_context += f"\n\nINFORMATIONS UTILISATEUR:\n{context['user_info']}"
            
            if 'ticket_context' in context:
                system_context += f"\n\nCONTEXTE DU TICKET:\n{context['ticket_context']}"
        
        full_prompt = f"{system_context}\n\nQUESTION DE L'UTILISATEUR:\n{user_prompt}\n\nRÉPONSE:"
        
        return full_prompt
    
    def _calculate_confidence(self, response_text: str, candidate) -> float:
        """Calcule un score de confiance pour la réponse"""
        confidence = 0.5  # Score de base
        
        # Facteurs qui augmentent la confiance
        if len(response_text) > 50:  # Réponse substantielle
            confidence += 0.1
        
        if any(keyword in response_text.lower() for keyword in [
            'pos', 'caisse', 'inventaire', 'vente', 'produit', 'client'
        ]):  # Contient des mots-clés pertinents
            confidence += 0.2
        
        if '?' not in response_text:  # Réponse affirmative
            confidence += 0.1
        
        # Facteurs qui diminuent la confiance
        if any(phrase in response_text.lower() for phrase in [
            'je ne sais pas', 'je ne peux pas', 'désolé', 'impossible'
        ]):
            confidence -= 0.2
        
        if len(response_text) < 20:  # Réponse trop courte
            confidence -= 0.1
        
        # S'assurer que la confiance reste dans les limites
        return max(0.1, min(1.0, confidence))
    
    def _get_fallback_response(self) -> str:
        """Retourne une réponse de secours quand Gemini ne peut pas répondre"""
        return """Je suis désolé, je ne peux pas traiter votre demande pour le moment. 
        
Voici ce que vous pouvez faire :
1. Reformuler votre question de manière différente
2. Consulter notre base de connaissances
3. Créer un ticket de support pour une assistance personnalisée
4. Contacter directement notre équipe de support

Notre équipe est là pour vous aider !"""
    
    def classify_request(self, message: str) -> Dict[str, any]:
        """
        Classifie une demande utilisateur pour déterminer la catégorie et la priorité
        
        Args:
            message: Le message de l'utilisateur
            
        Returns:
            Dict contenant la catégorie, priorité et mots-clés détectés
        """
        if not self.is_available():
            return self._get_default_classification()
        
        try:
            classification_prompt = f"""
Analyse ce message de support et classifie-le selon ces catégories:

CATÉGORIES:
- technical: Problèmes techniques, bugs, erreurs
- billing: Questions de facturation, paiements
- general: Questions générales, aide d'utilisation
- feature_request: Demandes de nouvelles fonctionnalités
- bug_report: Rapports de bugs spécifiques
- training: Demandes de formation, tutoriels
- integration: Questions d'intégration, API

PRIORITÉS:
- low: Questions générales, demandes d'information
- medium: Problèmes qui affectent l'utilisation normale
- high: Problèmes qui bloquent des fonctionnalités importantes
- urgent: Problèmes critiques qui empêchent l'utilisation du système

MESSAGE À ANALYSER:
"{message}"

Réponds uniquement avec un JSON valide dans ce format:
{{
    "category": "nom_de_la_catégorie",
    "priority": "niveau_de_priorité",
    "keywords": ["mot1", "mot2", "mot3"],
    "confidence": 0.85
}}
"""
            
            response = self.model.generate_content(classification_prompt)
            
            if response.candidates and len(response.candidates) > 0:
                response_text = response.candidates[0].content.parts[0].text
                
                try:
                    # Extraire le JSON de la réponse
                    json_start = response_text.find('{')
                    json_end = response_text.rfind('}') + 1
                    
                    if json_start >= 0 and json_end > json_start:
                        json_str = response_text[json_start:json_end]
                        classification = json.loads(json_str)
                        
                        # Valider les champs requis
                        if all(key in classification for key in ['category', 'priority']):
                            return classification
                
                except json.JSONDecodeError:
                    logger.warning("Impossible de parser la classification JSON")
            
        except Exception as e:
            logger.error(f"Erreur lors de la classification: {str(e)}")
        
        return self._get_default_classification()
    
    def _get_default_classification(self) -> Dict[str, any]:
        """Retourne une classification par défaut"""
        return {
            "category": "general",
            "priority": "medium",
            "keywords": [],
            "confidence": 0.3
        }
    
    def should_escalate(self, conversation_history: List[Dict], confidence_scores: List[float]) -> Tuple[bool, str]:
        """
        Détermine si une conversation doit être escaladée vers un agent humain
        
        Args:
            conversation_history: Historique de la conversation
            confidence_scores: Scores de confiance des réponses IA
            
        Returns:
            Tuple[bool, str]: (doit_escalader, raison)
        """
        # Critères d'escalade automatique
        
        # 1. Confiance moyenne trop faible
        if confidence_scores:
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            if avg_confidence < current_app.config.get('AI_CONFIDENCE_THRESHOLD', 0.7):
                return True, "Confiance de l'IA insuffisante"
        
        # 2. Conversation trop longue
        max_length = current_app.config.get('AI_MAX_CONVERSATION_LENGTH', 20)
        if len(conversation_history) > max_length:
            return True, "Conversation trop longue sans résolution"
        
        # 3. Mots-clés indiquant une frustration ou demande d'escalade
        last_messages = conversation_history[-3:] if len(conversation_history) >= 3 else conversation_history
        
        escalation_keywords = [
            'agent humain', 'parler à quelqu\'un', 'pas satisfait', 'n\'aide pas',
            'problème urgent', 'critique', 'ne fonctionne pas', 'bug grave'
        ]
        
        for message in last_messages:
            if message.get('sender_type') == 'user':
                content = message.get('content', '').lower()
                if any(keyword in content for keyword in escalation_keywords):
                    return True, "Demande explicite d'escalade ou frustration détectée"
        
        return False, ""

# Instance globale du service
gemini_service = GeminiService()

class SupportAIService:
    """Service principal pour le support AI utilisant Gemini"""

    def __init__(self):
        self.gemini = gemini_service

    def process_chat_message(self, message: str, conversation_id: int, user_id: int) -> Dict[str, any]:
        """
        Traite un message de chat et génère une réponse IA

        Args:
            message: Message de l'utilisateur
            conversation_id: ID de la conversation
            user_id: ID de l'utilisateur

        Returns:
            Dict contenant la réponse et les métadonnées
        """
        from .models import SupportConversation, SupportChatMessage, MessageSender
        from app.extensions import db

        try:
            # Récupérer le contexte de la conversation
            conversation = SupportConversation.query.get(conversation_id)
            if not conversation:
                raise ValueError("Conversation non trouvée")

            # Récupérer l'historique récent
            recent_messages = SupportChatMessage.query.filter_by(conversation_id=conversation_id)\
                                                    .order_by(SupportChatMessage.created_at.desc())\
                                                    .limit(10).all()

            # Construire le contexte
            context = self._build_conversation_context(recent_messages, user_id)

            # Générer la réponse avec Gemini
            start_time = time.time()
            response_text, confidence = self.gemini.generate_response(message, context)
            processing_time = time.time() - start_time

            # Vérifier si escalade nécessaire
            conversation_history = [
                {
                    'sender_type': msg.sender_type.value,
                    'content': msg.content,
                    'created_at': msg.created_at.isoformat()
                }
                for msg in reversed(recent_messages)
            ]

            confidence_scores = [msg.ai_confidence for msg in recent_messages
                               if msg.ai_confidence is not None]
            confidence_scores.append(confidence)

            should_escalate, escalation_reason = self.gemini.should_escalate(
                conversation_history, confidence_scores
            )

            return {
                'success': True,
                'response': response_text,
                'confidence': confidence,
                'processing_time': processing_time,
                'should_escalate': should_escalate,
                'escalation_reason': escalation_reason,
                'model_used': current_app.config.get('GEMINI_MODEL', 'gemini-2.0-flash-exp')
            }

        except Exception as e:
            logger.error(f"Erreur lors du traitement du message: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'response': self.gemini._get_fallback_response(),
                'confidence': 0.1,
                'processing_time': 0,
                'should_escalate': True,
                'escalation_reason': 'Erreur technique'
            }

    def process_ticket_message(self, message: str, ticket_id: int, user_id: int) -> Dict[str, any]:
        """
        Traite un message de ticket et génère une réponse IA

        Args:
            message: Message de l'utilisateur
            ticket_id: ID du ticket
            user_id: ID de l'utilisateur

        Returns:
            Dict contenant la réponse et les métadonnées
        """
        from .models import SupportTicket, SupportMessage, MessageSender
        from app.extensions import db

        try:
            # Récupérer le contexte du ticket
            ticket = SupportTicket.query.get(ticket_id)
            if not ticket:
                raise ValueError("Ticket non trouvé")

            # Récupérer l'historique du ticket
            ticket_messages = SupportMessage.query.filter_by(ticket_id=ticket_id)\
                                                 .order_by(SupportMessage.created_at.asc()).all()

            # Construire le contexte
            context = self._build_ticket_context(ticket, ticket_messages, user_id)

            # Classifier la demande
            classification = self.gemini.classify_request(message)

            # Générer la réponse avec Gemini
            start_time = time.time()
            response_text, confidence = self.gemini.generate_response(message, context)
            processing_time = time.time() - start_time

            return {
                'success': True,
                'response': response_text,
                'confidence': confidence,
                'processing_time': processing_time,
                'classification': classification,
                'model_used': current_app.config.get('GEMINI_MODEL', 'gemini-2.0-flash-exp')
            }

        except Exception as e:
            logger.error(f"Erreur lors du traitement du message de ticket: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'response': self.gemini._get_fallback_response(),
                'confidence': 0.1,
                'processing_time': 0
            }

    def _build_conversation_context(self, messages: List, user_id: int) -> Dict[str, str]:
        """Construit le contexte pour une conversation de chat"""
        from app.modules.auth.models import User

        # Informations utilisateur
        user = User.query.get(user_id)
        user_info = f"Utilisateur: {user.username} ({user.role.value})" if user else "Utilisateur inconnu"

        # Historique de conversation
        conversation_history = []
        for msg in reversed(messages):
            sender = "Utilisateur" if msg.sender_type.value == "user" else "IA"
            conversation_history.append(f"{sender}: {msg.content}")

        return {
            'user_info': user_info,
            'conversation_history': '\n'.join(conversation_history[-5:])  # 5 derniers messages
        }

    def _build_ticket_context(self, ticket, messages: List, user_id: int) -> Dict[str, str]:
        """Construit le contexte pour un ticket de support"""
        from app.modules.auth.models import User

        # Informations utilisateur
        user = User.query.get(user_id)
        user_info = f"Utilisateur: {user.username} ({user.role.value})" if user else "Utilisateur inconnu"

        # Contexte du ticket
        ticket_context = f"""
Ticket: {ticket.ticket_number}
Titre: {ticket.title}
Catégorie: {ticket.category.value}
Priorité: {ticket.priority.value}
Statut: {ticket.status.value}
Description initiale: {ticket.description}
"""

        # Historique des messages
        message_history = []
        for msg in messages:
            sender = "Utilisateur" if msg.sender_type.value == "user" else "IA"
            message_history.append(f"{sender}: {msg.content}")

        return {
            'user_info': user_info,
            'ticket_context': ticket_context,
            'conversation_history': '\n'.join(message_history)
        }

# Instance globale du service de support AI
support_ai_service = SupportAIService()
