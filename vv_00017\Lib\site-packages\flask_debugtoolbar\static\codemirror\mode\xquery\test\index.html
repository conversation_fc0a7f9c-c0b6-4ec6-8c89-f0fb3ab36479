<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
      "http://www.w3.org/TR/html4/loose.dtd">
<html>
  <head>
    <link rel="stylesheet" href="http://code.jquery.com/qunit/qunit-git.css" type="text/css"/>
    <script src="http://code.jquery.com/jquery-latest.js"> </script>
    <script type="text/javascript" src="http://code.jquery.com/qunit/qunit-git.js"></script>

    <script src="../../../lib/codemirror.js"></script> 
    <script src="../xquery.js"></script>

    <script type="text/javascript" src="testBase.js"></script>
    <script type="text/javascript" src="testMultiAttr.js"></script>
    <script type="text/javascript" src="testQuotes.js"></script>
    <script type="text/javascript" src="testEmptySequenceKeyword.js"></script>
    <script type="text/javascript" src="testProcessingInstructions.js"></script>
    <script type="text/javascript" src="testNamespaces.js"></script>
  </head>
  <body>
    <h1 id="qunit-header">XQuery CodeMirror Mode</h1>
    <h2 id="qunit-banner"></h2>
    <h2 id="qunit-userAgent"></h2>
    <ol id="qunit-tests">
    </ol>
    <div id="sandbox" style="right:5000px; position:absolute; "></div>
  </body>
</html>
