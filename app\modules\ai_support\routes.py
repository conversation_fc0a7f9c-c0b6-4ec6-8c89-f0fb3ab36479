from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app.extensions import db
from . import bp
from .models import (SupportTicket, SupportMessage, SupportConversation, 
                    SupportChatMessage, SupportKnowledgeBase, SupportAnalytics,
                    SupportTicketStatus, SupportTicketPriority, SupportTicketCategory,
                    MessageSender)
from .forms import (CreateTicketForm, UpdateTicketForm, AddMessageForm, 
                   ChatMessageForm, SatisfactionForm, KnowledgeBaseForm,
                   SearchForm, EscalationForm, AIConfigForm)
from datetime import datetime, timedelta
import uuid

@bp.route('/')
@login_required
def index():
    """Page d'accueil du support AI"""
    # Statistiques pour l'utilisateur actuel
    user_tickets = SupportTicket.query.filter_by(user_id=current_user.id).all()
    
    stats = {
        'total_tickets': len(user_tickets),
        'open_tickets': len([t for t in user_tickets if t.status in [SupportTicketStatus.OPEN, SupportTicketStatus.IN_PROGRESS]]),
        'resolved_tickets': len([t for t in user_tickets if t.status == SupportTicketStatus.RESOLVED]),
        'active_conversations': SupportConversation.query.filter_by(user_id=current_user.id, is_active=True).count()
    }
    
    # Tickets récents
    recent_tickets = SupportTicket.query.filter_by(user_id=current_user.id)\
                                       .order_by(SupportTicket.created_at.desc())\
                                       .limit(5).all()
    
    return render_template('ai_support/index.html', stats=stats, recent_tickets=recent_tickets)

@bp.route('/chat')
@login_required
def chat():
    """Interface de chat en temps réel avec l'IA"""
    # Créer ou récupérer une conversation active
    conversation = SupportConversation.query.filter_by(
        user_id=current_user.id, 
        is_active=True
    ).first()
    
    if not conversation:
        conversation = SupportConversation(
            session_id=str(uuid.uuid4()),
            user_id=current_user.id
        )
        db.session.add(conversation)
        db.session.commit()
    
    # Récupérer les messages de la conversation
    messages = SupportChatMessage.query.filter_by(conversation_id=conversation.id)\
                                      .order_by(SupportChatMessage.created_at.asc()).all()
    
    form = ChatMessageForm()
    return render_template('ai_support/chat.html', 
                         conversation=conversation, 
                         messages=messages, 
                         form=form)

@bp.route('/tickets')
@login_required
def tickets():
    """Liste des tickets de l'utilisateur"""
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # Filtres
    status_filter = request.args.get('status')
    category_filter = request.args.get('category')
    
    query = SupportTicket.query.filter_by(user_id=current_user.id)
    
    if status_filter:
        query = query.filter(SupportTicket.status == SupportTicketStatus(status_filter))
    
    if category_filter:
        query = query.filter(SupportTicket.category == SupportTicketCategory(category_filter))
    
    tickets = query.order_by(SupportTicket.created_at.desc())\
                  .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('ai_support/tickets.html', tickets=tickets)

@bp.route('/tickets/create', methods=['GET', 'POST'])
@login_required
def create_ticket():
    """Créer un nouveau ticket de support"""
    form = CreateTicketForm()
    
    if form.validate_on_submit():
        ticket = SupportTicket(
            ticket_number=SupportTicket().generate_ticket_number(),
            title=form.title.data,
            description=form.description.data,
            category=SupportTicketCategory(form.category.data),
            priority=SupportTicketPriority(form.priority.data),
            user_id=current_user.id
        )
        
        db.session.add(ticket)
        db.session.commit()
        
        # Créer le premier message avec la description
        initial_message = SupportMessage(
            ticket_id=ticket.id,
            content=form.description.data,
            sender_type=MessageSender.USER,
            sender_id=current_user.id
        )
        db.session.add(initial_message)
        db.session.commit()
        
        flash(f'Ticket {ticket.ticket_number} créé avec succès!', 'success')
        return redirect(url_for('ai_support.view_ticket', id=ticket.id))
    
    return render_template('ai_support/create_ticket.html', form=form)

@bp.route('/tickets/<int:id>')
@login_required
def view_ticket(id):
    """Voir les détails d'un ticket"""
    ticket = SupportTicket.query.get_or_404(id)
    
    # Vérifier que l'utilisateur peut voir ce ticket
    if ticket.user_id != current_user.id and not current_user.is_admin:
        flash('Vous n\'avez pas accès à ce ticket.', 'error')
        return redirect(url_for('ai_support.tickets'))
    
    # Récupérer les messages du ticket
    messages = SupportMessage.query.filter_by(ticket_id=ticket.id)\
                                  .order_by(SupportMessage.created_at.asc()).all()
    
    # Formulaires
    message_form = AddMessageForm()
    satisfaction_form = SatisfactionForm()
    escalation_form = EscalationForm()
    
    return render_template('ai_support/view_ticket.html', 
                         ticket=ticket, 
                         messages=messages,
                         message_form=message_form,
                         satisfaction_form=satisfaction_form,
                         escalation_form=escalation_form)

@bp.route('/tickets/<int:id>/add_message', methods=['POST'])
@login_required
def add_message(id):
    """Ajouter un message à un ticket"""
    ticket = SupportTicket.query.get_or_404(id)
    
    # Vérifier les permissions
    if ticket.user_id != current_user.id and not current_user.is_admin:
        flash('Vous n\'avez pas accès à ce ticket.', 'error')
        return redirect(url_for('ai_support.tickets'))
    
    form = AddMessageForm()
    
    if form.validate_on_submit():
        message = SupportMessage(
            ticket_id=ticket.id,
            content=form.content.data,
            sender_type=MessageSender.USER,
            sender_id=current_user.id,
            is_internal=form.is_internal.data if current_user.is_admin else False
        )
        
        db.session.add(message)
        
        # Mettre à jour le statut du ticket si nécessaire
        if ticket.status == SupportTicketStatus.RESOLVED:
            ticket.status = SupportTicketStatus.IN_PROGRESS
        
        ticket.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash('Message ajouté avec succès!', 'success')
        
        # TODO: Déclencher une réponse de l'IA
        
    return redirect(url_for('ai_support.view_ticket', id=id))

@bp.route('/knowledge-base')
@login_required
def knowledge_base():
    """Base de connaissances"""
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # Filtres
    category_filter = request.args.get('category')
    search_query = request.args.get('q')
    
    query = SupportKnowledgeBase.query.filter_by(is_active=True)
    
    if category_filter:
        query = query.filter(SupportKnowledgeBase.category == SupportTicketCategory(category_filter))
    
    if search_query:
        query = query.filter(
            db.or_(
                SupportKnowledgeBase.title.contains(search_query),
                SupportKnowledgeBase.content.contains(search_query),
                SupportKnowledgeBase.tags.contains(search_query)
            )
        )
    
    articles = query.order_by(SupportKnowledgeBase.usage_count.desc())\
                   .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('ai_support/knowledge_base.html', articles=articles)

@bp.route('/knowledge-base/<int:id>')
@login_required
def view_article(id):
    """Voir un article de la base de connaissances"""
    article = SupportKnowledgeBase.query.get_or_404(id)
    
    # Incrémenter le compteur d'utilisation
    article.usage_count += 1
    article.last_used = datetime.utcnow()
    db.session.commit()
    
    return render_template('ai_support/view_article.html', article=article)

# API Routes pour le chat en temps réel
@bp.route('/api/chat/send', methods=['POST'])
@login_required
def api_send_message():
    """API pour envoyer un message de chat"""
    from .gemini_service import support_ai_service

    data = request.get_json()

    # Debug: Log des données reçues
    current_app.logger.info(f"Données reçues dans api_send_message: {data}")
    current_app.logger.info(f"Utilisateur: {current_user.id}")

    if not data:
        current_app.logger.error("Aucune donnée JSON reçue")
        return jsonify({'error': 'Aucune donnée reçue'}), 400

    if 'message' not in data:
        current_app.logger.error("Champ 'message' manquant")
        return jsonify({'error': 'Message manquant'}), 400

    # Si pas de conversation_id, créer une nouvelle conversation
    conversation = None
    if 'conversation_id' in data and data['conversation_id']:
        try:
            conversation_id = int(data['conversation_id'])
            conversation = SupportConversation.query.filter_by(
                id=conversation_id,
                user_id=current_user.id
            ).first()
        except (ValueError, TypeError):
            current_app.logger.warning(f"ID de conversation invalide: {data.get('conversation_id')}")

    # Créer une nouvelle conversation si nécessaire
    if not conversation:
        current_app.logger.info("Création d'une nouvelle conversation")
        conversation = SupportConversation(
            session_id=str(uuid.uuid4()),
            user_id=current_user.id
        )
        db.session.add(conversation)
        db.session.commit()
        current_app.logger.info(f"Nouvelle conversation créée: {conversation.id}")

    current_app.logger.info(f"Conversation utilisée: {conversation.id}, utilisateur: {conversation.user_id}")

    # Créer le message utilisateur
    user_message = SupportChatMessage(
        conversation_id=conversation.id,
        content=data['message'],
        sender_type=MessageSender.USER,
        sender_id=current_user.id
    )

    db.session.add(user_message)
    conversation.total_messages += 1
    db.session.commit()

    # Générer une réponse de l'IA
    try:
        current_app.logger.info(f"Début du traitement IA pour le message: {data['message'][:50]}...")

        # Vérifier d'abord que le service est disponible
        from .gemini_service import gemini_service

        current_app.logger.info(f"Service Gemini disponible: {gemini_service.is_available()}")

        if not gemini_service.is_available():
            current_app.logger.warning("Service Gemini non disponible, tentative de reconfiguration...")
            # Essayer de reconfigurer
            gemini_service._configure()

            if not gemini_service.is_available():
                raise Exception("Service Gemini non disponible. Vérifiez votre configuration GEMINI_API_KEY.")

        current_app.logger.info("Appel de support_ai_service.process_chat_message...")
        ai_result = support_ai_service.process_chat_message(
            data['message'],
            conversation.id,
            current_user.id
        )
        current_app.logger.info(f"Résultat IA reçu: {ai_result.get('success', False)}")

        if ai_result['success']:
            ai_message = SupportChatMessage(
                conversation_id=conversation.id,
                content=ai_result['response'],
                sender_type=MessageSender.AI,
                ai_confidence=ai_result['confidence'],
                ai_model_used=ai_result['model_used'],
                processing_time=ai_result['processing_time']
            )

            db.session.add(ai_message)
            conversation.total_messages += 1
            conversation.ai_responses += 1

            # Vérifier si escalade nécessaire
            if ai_result.get('should_escalate', False):
                # Créer un ticket automatiquement
                ticket = SupportTicket(
                    ticket_number=SupportTicket().generate_ticket_number(),
                    title=f"Escalade automatique - Chat {conversation.session_id[:8]}",
                    description=f"Conversation escaladée automatiquement.\nRaison: {ai_result.get('escalation_reason', 'Confiance insuffisante')}\n\nDernier message: {data['message']}",
                    category=SupportTicketCategory.GENERAL,
                    priority=SupportTicketPriority.MEDIUM,
                    user_id=current_user.id,
                    escalated_to_human=True,
                    escalation_reason=ai_result.get('escalation_reason', 'Escalade automatique')
                )
                db.session.add(ticket)

                # Ajouter un message système
                system_message = SupportChatMessage(
                    conversation_id=conversation.id,
                    content=f"Cette conversation a été escaladée vers un agent humain. Un ticket ({ticket.ticket_number}) a été créé automatiquement.",
                    sender_type=MessageSender.SYSTEM
                )
                db.session.add(system_message)

            db.session.commit()

            response_data = {
                'success': True,
                'conversation_id': conversation.id,
                'user_message': {
                    'id': user_message.id,
                    'content': user_message.content,
                    'created_at': user_message.created_at.isoformat(),
                    'sender_type': user_message.sender_type.value
                },
                'ai_message': {
                    'id': ai_message.id,
                    'content': ai_message.content,
                    'created_at': ai_message.created_at.isoformat(),
                    'sender_type': ai_message.sender_type.value,
                    'confidence': ai_message.ai_confidence
                }
            }

            if ai_result.get('should_escalate', False):
                response_data['escalated'] = True
                response_data['ticket_number'] = ticket.ticket_number

            return jsonify(response_data)
        else:
            # Erreur dans le traitement IA, utiliser la réponse de fallback
            ai_message = SupportChatMessage(
                conversation_id=conversation.id,
                content=ai_result['response'],
                sender_type=MessageSender.AI,
                ai_confidence=ai_result['confidence']
            )

            db.session.add(ai_message)
            conversation.total_messages += 1
            conversation.ai_responses += 1
            db.session.commit()

            return jsonify({
                'success': True,
                'user_message': {
                    'id': user_message.id,
                    'content': user_message.content,
                    'created_at': user_message.created_at.isoformat(),
                    'sender_type': user_message.sender_type.value
                },
                'ai_message': {
                    'id': ai_message.id,
                    'content': ai_message.content,
                    'created_at': ai_message.created_at.isoformat(),
                    'sender_type': ai_message.sender_type.value,
                    'confidence': ai_message.ai_confidence
                },
                'error': ai_result.get('error', 'Erreur inconnue')
            })

    except Exception as e:
        error_msg = str(e)
        current_app.logger.error(f"Erreur lors du traitement du message: {error_msg}")

        # Message d'erreur plus informatif selon le type d'erreur
        if "GEMINI_API_KEY" in error_msg or "non disponible" in error_msg:
            ai_content = """🔧 **Configuration requise**

Il semble que l'IA ne soit pas encore configurée. Pour activer le support automatisé :

1. **Obtenez une clé API Gemini** sur [Google AI Studio](https://makersuite.google.com/app/apikey)
2. **Ajoutez-la dans votre fichier .env** :
   ```
   GEMINI_API_KEY=votre-cle-api-ici
   ```
3. **Redémarrez l'application**

En attendant, vous pouvez :
- 📚 Consulter notre [base de connaissances](/support/knowledge-base)
- 🎫 [Créer un ticket](/support/tickets/create) pour une assistance personnalisée"""
        else:
            ai_content = f"""⚠️ **Problème technique temporaire**

Je rencontre actuellement des difficultés techniques.

**Que faire ?**
- 🔄 Réessayez dans quelques instants
- 📚 Consultez notre [base de connaissances](/support/knowledge-base)
- 🎫 [Créez un ticket](/support/tickets/create) pour une assistance personnalisée

**Détails techniques :** {error_msg[:100]}..."""

        ai_message = SupportChatMessage(
            conversation_id=conversation.id,
            content=ai_content,
            sender_type=MessageSender.SYSTEM
        )

        db.session.add(ai_message)
        conversation.total_messages += 1
        db.session.commit()

        return jsonify({
            'success': False,
            'error': 'Erreur technique',
            'error_details': error_msg,
            'conversation_id': conversation.id,
            'user_message': {
                'id': user_message.id,
                'content': user_message.content,
                'created_at': user_message.created_at.isoformat(),
                'sender_type': user_message.sender_type.value
            },
            'ai_message': {
                'id': ai_message.id,
                'content': ai_message.content,
                'created_at': ai_message.created_at.isoformat(),
                'sender_type': ai_message.sender_type.value,
                'confidence': 0.1
            }
        })

@bp.route('/api/chat/history/<int:conversation_id>')
@login_required
def api_chat_history(conversation_id):
    """API pour récupérer l'historique d'une conversation"""
    conversation = SupportConversation.query.get_or_404(conversation_id)
    
    # Vérifier les permissions
    if conversation.user_id != current_user.id:
        return jsonify({'error': 'Accès non autorisé'}), 403
    
    messages = SupportChatMessage.query.filter_by(conversation_id=conversation.id)\
                                      .order_by(SupportChatMessage.created_at.asc()).all()
    
    return jsonify({
        'messages': [{
            'id': msg.id,
            'content': msg.content,
            'sender_type': msg.sender_type.value,
            'created_at': msg.created_at.isoformat(),
            'confidence': msg.ai_confidence
        } for msg in messages]
    })

@bp.route('/api/tickets/<int:id>/escalate', methods=['POST'])
@login_required
def api_escalate_ticket(id):
    """API pour escalader un ticket vers un agent humain"""
    ticket = SupportTicket.query.get_or_404(id)
    
    # Vérifier les permissions
    if ticket.user_id != current_user.id:
        return jsonify({'error': 'Accès non autorisé'}), 403
    
    data = request.get_json()
    
    if not data or 'reason' not in data:
        return jsonify({'error': 'Raison d\'escalade manquante'}), 400
    
    # Mettre à jour le ticket
    ticket.escalated_to_human = True
    ticket.escalation_reason = data['reason']
    ticket.status = SupportTicketStatus.WAITING_HUMAN
    ticket.updated_at = datetime.utcnow()
    
    if 'priority' in data:
        ticket.priority = SupportTicketPriority(data['priority'])
    
    # Ajouter un message système
    escalation_message = SupportMessage(
        ticket_id=ticket.id,
        content=f"Ticket escaladé vers un agent humain. Raison: {data['reason']}",
        sender_type=MessageSender.SYSTEM
    )
    
    db.session.add(escalation_message)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'Ticket escaladé avec succès'})

# Routes d'administration (pour les agents)
@bp.route('/admin')
@login_required
def admin_dashboard():
    """Tableau de bord d'administration du support"""
    if not current_user.is_admin:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('ai_support.index'))
    
    # Statistiques générales
    today = datetime.utcnow().date()
    week_ago = today - timedelta(days=7)
    
    stats = {
        'total_tickets': SupportTicket.query.count(),
        'open_tickets': SupportTicket.query.filter(
            SupportTicket.status.in_([SupportTicketStatus.OPEN, SupportTicketStatus.IN_PROGRESS])
        ).count(),
        'escalated_tickets': SupportTicket.query.filter_by(escalated_to_human=True).count(),
        'tickets_today': SupportTicket.query.filter(SupportTicket.created_at >= today).count(),
        'active_conversations': SupportConversation.query.filter_by(is_active=True).count()
    }
    
    # Tickets récents nécessitant une attention
    urgent_tickets = SupportTicket.query.filter(
        db.or_(
            SupportTicket.status == SupportTicketStatus.WAITING_HUMAN,
            SupportTicket.priority == SupportTicketPriority.URGENT
        )
    ).order_by(SupportTicket.created_at.desc()).limit(10).all()
    
    return render_template('ai_support/admin_dashboard.html',
                         stats=stats,
                         urgent_tickets=urgent_tickets)

@bp.route('/api/test-gemini')
@login_required
def api_test_gemini():
    """API pour tester la connexion Gemini"""
    from .gemini_service import gemini_service
    import os

    if not current_user.is_admin:
        return jsonify({'error': 'Accès non autorisé'}), 403

    # Diagnostic détaillé
    diagnostic = {
        'api_key_configured': bool(os.environ.get('GEMINI_API_KEY')),
        'api_key_format': 'Correct' if os.environ.get('GEMINI_API_KEY', '').startswith('AIza') else 'Incorrect ou manquant',
        'service_configured': gemini_service.is_configured,
        'service_available': gemini_service.is_available(),
        'config_values': {
            'GEMINI_MODEL': gemini_service._get_config('GEMINI_MODEL'),
            'AI_SUPPORT_ENABLED': gemini_service._get_config('AI_SUPPORT_ENABLED'),
            'GEMINI_TEMPERATURE': gemini_service._get_config('GEMINI_TEMPERATURE'),
        }
    }

    result = gemini_service.test_connection()
    result['diagnostic'] = diagnostic

    return jsonify(result)

@bp.route('/api/test-simple')
@login_required
def api_test_simple():
    """Test simple de l'API pour vérifier que les routes fonctionnent"""
    return jsonify({
        'success': True,
        'message': 'API fonctionne',
        'user_id': current_user.id,
        'timestamp': datetime.utcnow().isoformat()
    })

@bp.route('/api/chat/send-test', methods=['POST'])
@login_required
def api_send_message_test():
    """Version de test de l'API de chat sans IA"""
    try:
        current_app.logger.info("=== DÉBUT API TEST ===")
        current_app.logger.info(f"Utilisateur connecté: {current_user.id}")
        current_app.logger.info(f"Méthode: {request.method}")
        current_app.logger.info(f"Content-Type: {request.content_type}")

        data = request.get_json()
        current_app.logger.info(f"Données JSON reçues: {data}")

        if not data:
            current_app.logger.error("Aucune donnée JSON reçue")
            return jsonify({'error': 'Aucune donnée JSON'}), 400

        if 'message' not in data:
            current_app.logger.error("Champ 'message' manquant dans les données")
            return jsonify({'error': 'Message manquant'}), 400

        current_app.logger.info(f"Message reçu: {data['message']}")

        # Version ultra-simple : juste retourner un succès
        current_app.logger.info("Retour d'une réponse de test simple")
        return jsonify({
            'success': True,
            'conversation_id': 1,
            'user_message': {
                'id': 1,
                'content': data['message'],
                'created_at': datetime.utcnow().isoformat(),
                'sender_type': 'user'
            },
            'ai_message': {
                'id': 2,
                'content': f"Test simple reçu: {data['message']}",
                'created_at': datetime.utcnow().isoformat(),
                'sender_type': 'ai',
                'confidence': 0.9
            }
        })

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la réception des données: {str(e)}")
        return jsonify({'error': f'Erreur de traitement: {str(e)}'}), 400

@bp.route('/api/debug-chat')
@login_required
def api_debug_chat():
    """API pour déboguer les problèmes de chat"""
    from .gemini_service import gemini_service, support_ai_service
    import os

    debug_info = {
        'timestamp': datetime.utcnow().isoformat(),
        'user_id': current_user.id,
        'environment': {
            'GEMINI_API_KEY_SET': bool(os.environ.get('GEMINI_API_KEY')),
            'GEMINI_API_KEY_FORMAT': 'Valid' if os.environ.get('GEMINI_API_KEY', '').startswith('AIza') else 'Invalid',
            'AI_SUPPORT_ENABLED': os.environ.get('AI_SUPPORT_ENABLED', 'Not set'),
        },
        'service_status': {
            'gemini_configured': gemini_service.is_configured,
            'gemini_available': gemini_service.is_available(),
            'model_name': gemini_service._get_config('GEMINI_MODEL', 'Not configured'),
        }
    }

    # Test simple
    try:
        if gemini_service.is_available():
            test_result = gemini_service.test_connection()
            debug_info['connection_test'] = test_result
        else:
            debug_info['connection_test'] = {'success': False, 'error': 'Service not available'}
    except Exception as e:
        debug_info['connection_test'] = {'success': False, 'error': str(e)}

    return jsonify(debug_info)

@bp.route('/api/init-sample-data')
@login_required
def api_init_sample_data():
    """Initialise des données d'exemple pour la base de connaissances"""
    if not current_user.is_admin:
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        # Vérifier si des articles existent déjà
        existing_count = SupportKnowledgeBase.query.count()
        if existing_count > 0:
            return jsonify({
                'success': False,
                'message': f'{existing_count} articles existent déjà. Supprimez-les d\'abord si vous voulez réinitialiser.'
            })

        # Articles d'exemple
        sample_articles = [
            {
                'title': 'Comment utiliser la caisse enregistreuse',
                'content': '''# Guide d'utilisation de la caisse enregistreuse

## Démarrage
1. Connectez-vous avec vos identifiants
2. Sélectionnez votre caisse dans le menu principal
3. Vérifiez que la caisse est ouverte

## Effectuer une vente
1. Scannez ou saisissez les codes-barres des produits
2. Vérifiez les quantités et prix
3. Appliquez les remises si nécessaire
4. Sélectionnez le mode de paiement
5. Encaissez et remettez la monnaie
6. Imprimez le reçu

## Problèmes courants
- **Scanner ne fonctionne pas** : Vérifiez la connexion USB
- **Produit non trouvé** : Saisissez manuellement le code
- **Erreur de prix** : Contactez un superviseur''',
                'category': SupportTicketCategory.TRAINING,
                'tags': 'caisse, vente, scanner, paiement'
            },
            {
                'title': 'Gestion de l\'inventaire',
                'content': '''# Gestion de l'inventaire

## Ajouter un produit
1. Allez dans le menu Inventaire
2. Cliquez sur "Nouveau produit"
3. Remplissez les informations obligatoires
4. Définissez le prix et les taxes
5. Sauvegardez

## Mise à jour des stocks
1. Accédez à la liste des produits
2. Cliquez sur le produit à modifier
3. Ajustez la quantité en stock
4. Indiquez la raison de l'ajustement

## Alertes de stock
Le système vous alertera automatiquement quand :
- Le stock atteint le seuil minimum
- Un produit est en rupture
- Une commande fournisseur est recommandée''',
                'category': SupportTicketCategory.TRAINING,
                'tags': 'inventaire, stock, produit, alerte'
            },
            {
                'title': 'Résolution des problèmes de connexion',
                'content': '''# Problèmes de connexion

## Symptômes courants
- Page qui ne charge pas
- Erreur "Connexion impossible"
- Déconnexions fréquentes

## Solutions
1. **Vérifiez votre connexion internet**
   - Testez avec un autre site web
   - Redémarrez votre routeur si nécessaire

2. **Videz le cache du navigateur**
   - Ctrl+Shift+Suppr (Chrome/Firefox)
   - Sélectionnez "Tout" et confirmez

3. **Vérifiez les paramètres du navigateur**
   - Désactivez temporairement les extensions
   - Autorisez les cookies pour notre site

4. **Contactez le support**
   Si le problème persiste, créez un ticket avec :
   - Votre navigateur et version
   - Le message d'erreur exact
   - L'heure du problème''',
                'category': SupportTicketCategory.TECHNICAL,
                'tags': 'connexion, internet, navigateur, cache'
            },
            {
                'title': 'Génération de rapports de vente',
                'content': '''# Rapports de vente

## Types de rapports disponibles
- Ventes par jour/semaine/mois
- Ventes par produit
- Ventes par employé
- Analyse des tendances

## Générer un rapport
1. Allez dans le menu Rapports
2. Sélectionnez le type de rapport
3. Choisissez la période
4. Filtrez par catégorie si nécessaire
5. Cliquez sur "Générer"

## Exporter les données
- Format PDF pour impression
- Format Excel pour analyse
- Format CSV pour import

## Planification automatique
Vous pouvez programmer l'envoi automatique de rapports :
- Quotidiens par email
- Hebdomadaires le lundi
- Mensuels le 1er du mois''',
                'category': SupportTicketCategory.TRAINING,
                'tags': 'rapport, vente, export, planification'
            },
            {
                'title': 'Problèmes d\'impression',
                'content': '''# Résolution des problèmes d'impression

## Vérifications de base
1. L'imprimante est-elle allumée ?
2. Y a-t-il du papier ?
3. Les câbles sont-ils bien connectés ?

## Problèmes courants

### Reçus qui ne s'impriment pas
- Vérifiez les pilotes d'imprimante
- Redémarrez le service d'impression
- Testez avec une autre application

### Qualité d'impression médiocre
- Nettoyez les têtes d'impression
- Vérifiez le niveau d'encre/toner
- Ajustez les paramètres de qualité

### Bourrage papier
- Éteignez l'imprimante
- Retirez délicatement le papier coincé
- Vérifiez qu'aucun morceau ne reste
- Rallumez l'imprimante

## Configuration
Pour configurer une nouvelle imprimante :
1. Allez dans Paramètres > Imprimantes
2. Cliquez sur "Ajouter une imprimante"
3. Suivez l'assistant de configuration''',
                'category': SupportTicketCategory.TECHNICAL,
                'tags': 'imprimante, reçu, bourrage, pilote'
            }
        ]

        # Créer les articles
        created_count = 0
        for article_data in sample_articles:
            article = SupportKnowledgeBase(
                title=article_data['title'],
                content=article_data['content'],
                category=article_data['category'],
                tags=article_data['tags'],
                created_by_id=current_user.id,
                is_active=True
            )
            db.session.add(article)
            created_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'{created_count} articles créés avec succès',
            'count': created_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
