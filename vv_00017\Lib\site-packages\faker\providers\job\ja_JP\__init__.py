from .. import Provider as BaseProvider


class Provider(BaseProvider):
    """
    source: https://ja.wikipedia.org/wiki/%E8%81%B7%E6%A5%AD%E4%B8%80%E8%A6%A7
    """

    jobs = [
        "アイドル",
        "アーティスト",
        "アートディレクター",
        "アナウンサー",
        "アニメーター",
        "医師",
        "イラストレーター",
        "医療事務員",
        "ウェディングプランナー",
        "ウェブデザイナー",
        "占い師",
        "運転士",
        "映画監督",
        "営業",
        "栄養士",
        "エステティシャン",
        "絵本作家",
        "演歌歌手",
        "エンジニア" "演奏家",
        "お笑い芸人",
        "音楽家",
        "音響技術者",
        "介護ヘルパー",
        "気象予報士",
        "脚本家",
        "救急救命士",
        "行政書士",
        "グラフィックデザイナー",
        "経営者",
        "検察官",
        "ゲームクリエイター",
        "建築家",
        "航海士",
        "コピーライター",
        "高等学校教員",
        "公認会計士",
        "公務員",
        "裁判官",
        "作曲家",
        "歯科医師",
        "司法書士",
        "小説家",
        "寿司職人",
        "測量士",
        "大学教授",
        "調理師",
        "電気工事士",
        "農家",
        "配管工",
        "バスガイド",
        "花火師",
        "漫画家",
        "モデル",
        "薬剤師",
        "YouTuber",
        "和紙職人",
    ]
