<!doctype html>
<html>
  <head>
    <title>CodeMirror: SPARQL mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="sparql.js"></script>
    <style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
    <link rel="stylesheet" href="../../doc/docs.css">
  </head>
  <body>
    <h1>CodeMirror: SPARQL mode</h1>
    <form><textarea id="code" name="code">
PREFIX a: &lt;http://www.w3.org/2000/10/annotation-ns#>
PREFIX dc: &lt;http://purl.org/dc/elements/1.1/>
PREFIX foaf: &lt;http://xmlns.com/foaf/0.1/>

# Comment!

SELECT ?given ?family
WHERE {
  ?annot a:annotates &lt;http://www.w3.org/TR/rdf-sparql-query/> .
  ?annot dc:creator ?c .
  OPTIONAL {?c foaf:given ?given ;
               foaf:family ?family } .
  FILTER isBlank(?c)
}
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: "application/x-sparql-query",
        tabMode: "indent",
        matchBrackets: true
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>application/x-sparql-query</code>.</p>

  </body>
</html>
