from .. import Provider as <PERSON><PERSON><PERSON><PERSON>


class Provider(<PERSON><PERSON><PERSON>ider):
    formats = (
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}}-{{last_name_male}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}}-{{last_name_female}}",
        "{{prefix_male}} {{first_name_male}} {{last_name_male}}",
        "{{prefix_female}} {{first_name_female}} {{last_name_female}}",
        "{{prefix_male}} {{first_name_male}} {{last_name_male}}",
        "{{prefix_female}} {{first_name_female}} {{last_name_female}}",
    )

    # names from https://en.wikipedia.org/wiki/Ghanaian_name,
    # https://buzz<PERSON>a.com/ghanaian-names/,
    # https://en.wikipedia.org/wiki/Akan_names,

    first_names_male = (
        "<PERSON>",
        "<PERSON>iku",
        "<PERSON>",
        "<PERSON>",
        "Akwesi",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "Carl",
        "Charles",
        "Christian",
        "Clifford",
        "Colins",
        "Daniel",
        "Danny",
        "David",
        "Denis",
        "Dennis",
        "Derrick",
        "Dominic",
        "Donald",
        "Douglas",
        "Duncan",
        "Edward",
        "Ekow",
        "Elliot",
        "Elliott",
        "Eric",
        "Fiifi",
        "Francis",
        "Frank",
        "Frederick",
        "George",
        "Gerald",
        "Gordon",
        "Graham",
        "Gregory",
        "Harry",
        "Henry",
        "Howard",
        "Isaac",
        "Akwasi",
        "Jack",
        "Jacob",
        "Jake",
        "James",
        "Jason",
        "Jeffrey",
        "Jeremy",
        "Joe",
        "Joel",
        "John",
        "Jonathan",
        "Joojo",
        "Joseph",
        "Josh",
        "Joshua",
        "Josiah",
        "Julian",
        "Justin",
        "Karl",
        "Kenneth",
        "Kevin",
        "Kofi",
        "Kojo",
        "Kujoe",
        "Kwabena",
        "Kwadwo",
        "Kwaku",
        "Kwame",
        "Kwamena",
        "Kwasi",
        "Kweku",
        "Kwesi",
        "Kyle",
        "Lawrence",
        "Leslie",
        "Louis",
        "Luke",
        "Malcolm",
        "Marcus",
        "Mark",
        "Martin",
        "Mathew",
        "Matthew",
        "Max",
        "Michael",
        "Nathan",
        "Nicholas",
        "Nigel",
        "Oliver",
        "Patrick",
        "Paul",
        "Peter",
        "Philip",
        "Phillip",
        "Raymond",
        "Richard",
        "Robert",
        "Roger",
        "Ronald",
        "Russell",
        "Sam",
        "Samuel",
        "Shaun",
        "Simon",
        "Stanley",
        "Stephen",
        "Steven",
        "Terence",
        "Thomas",
        "Timothy",
        "Tom",
        "Tony",
        "Victor",
        "Vincent",
        "William",
        "Yaw",
    )

    first_names_female = (
        "Aba",
        "Abena",
        "Abigail",
        "Adwoa",
        "Afia",
        "Afua",
        "Akos",
        "Akosua",
        "Akua",
        "Akumaa",
        "Alice",
        "Ama",
        "Amanda",
        "Amber",
        "Amelia",
        "Angela",
        "Ann",
        "Annette",
        "Awesi",
        "Baaba",
        "Barbara",
        "Beatrice",
        "COmfort",
        "Caroline",
        "Catherine",
        "Charlotte",
        "Christina",
        "Comfort",
        "Constance",
        "Danielle",
        "Deborah",
        "Debra",
        "Denise",
        "Dora",
        "Dorcas",
        "Dorothy",
        "Eliabeth",
        "Elizabeth",
        "Emily",
        "Emma",
        "Ernestina",
        "Esi",
        "Eunice",
        "Felicia",
        "Francesca",
        "Gemma",
        "Georgia",
        "Georgina",
        "Gifty",
        "Grace",
        "Grace",
        "Hannabel",
        "Hannah",
        "Harriet",
        "Helen",
        "Irene",
        "Janet",
        "Janet",
        "Janice",
        "Jasmine",
        "Jennifer",
        "Jessica",
        "Jill",
        "Joanna",
        "Josephine",
        "Joyce",
        "Joyce",
        "Judith",
        "Julia",
        "Juliana",
        "Julie",
        "Karen",
        "Kate",
        "Katherine",
        "Katy",
        "Lawrencia",
        "Linda",
        "Lisa",
        "Lorraine",
        "Lucy",
        "Lucy",
        "Lydia",
        "Lydia",
        "Mandy",
        "Margaret",
        "Margaret",
        "Maria",
        "Marian",
        "Marilyn",
        "Mary",
        "Mary",
        "Maureen",
        "Michelle",
        "Millicent",
        "Nana Ama",
        "Naomi",
        "Natalie",
        "Natasha",
        "Nicola",
        "Nimakoah",
        "Olivia",
        "Pamela",
        "Patricia",
        "Paula",
        "Priscilla",
        "Rachael",
        "Rachel",
        "Rebecca",
        "Rebecca",
        "Regina",
        "Rita",
        "Roselyn",
        "Rosemary",
        "Rosemary",
        "Ruth",
        "Salomey",
        "Samantha",
        "Sandra",
        "Sarah",
        "Sarah",
        "Sarah",
        "Sharon",
        "Sheila",
        "Shirley",
        "Stephanie",
        "Susan",
        "Susan",
        "Sylvia",
        "Teresa",
        "Tina",
        "Tracy",
        "Vanessa",
        "Veronica",
        "Victoria",
        "Vida",
        "Wendy",
        "Yaa",
        "Yvonne",
    )

    first_names = first_names_male + first_names_female

    last_names_male = (
        "Acheampong",
        "Adomah",
        "Adomako",
        "Adu",
        "Adusei",
        "Adutwum",
        "Afirifa",
        "Afoakwa",
        "Agyapong",
        "Agyapong",
        "Agyare",
        "Agyei",
        "Agyemang",
        "Ahortor",
        "Akoto",
        "Akowua",
        "Akyeamfuɔ",
        "Akyeampong",
        "Akyena",
        "Akyerεko",
        "Amo",
        "Amoa",
        "Amoako",
        "Amoasi",
        "Ampadu",
        "Ampofo",
        "Amponsah",
        "Andorful",
        "Ankra",
        "Anokye",
        "Ansa",
        "Antwi",
        "Antwi",
        "Appia",
        "Appiah",
        "Asamoa",
        "Asamoah",
        "Asante",
        "Asare",
        "Asenso",
        "Asiama",
        "Asiedu",
        "Ata",
        "Awuah",
        "Baa",
        "Baafi",
        "Baah",
        "Baawia",
        "Badu",
        "Boadi",
        "Boadu",
        "Boahen",
        "Boakye",
        "Boaten",
        "Boateng",
        "Bona",
        "Bonsra",
        "Bonsu",
        "Daako",
        "Danso",
        "Darko",
        "Donkor",
        "Duah",
        "Dwamena",
        "Fofie",
        "Fosu",
        "Gyamfi",
        "Gyasi",
        "Karikari",
        "Koomson",
        "Kumi",
        "Kusi",
        "Kwaakye",
        "Kwarteng",
        "Kyei",
        "Mensa",
        "Mensah",
        "Nkansa",
        "Nkansah",
        "Nkrumah",
        "Nsia",
        "Nti",
        "Ntiamoa",
        "Ntim",
        "Nyaako",
        "Nyame",
        "Nyantakyi",
        "Obeng",
        "Ofori",
        "Ofosu",
        "Okyere",
        "Omani",
        "Opoku",
        "Oppong",
        "Opuku",
        "Osei",
        "Oti",
        "Otiwa",
        "Otuo",
        "Owusu",
        "Prempeh",
        "Quartey",
        "Safo",
        "Sarpong",
        "Takyi",
        "Tawia",
        "Tutu",
        "Tweneboa",
        "Twumasi",
        "Wiafe",
        "Yaamoa",
        "Yawson",
        "Yeboa",
        "Yeboah",
        "Yirenkyi",
    )

    last_names_female = (
        "Aboraa",
        "Abrafi",
        "Acheampong",
        "Adoma",
        "Adomah",
        "Adomako",
        "Adu",
        "Adusei",
        "Adutwum",
        "Adutwumwaa",
        "Adwubi",
        "Afirifa",
        "Afoakwa",
        "Afrakomaa",
        "Agyapomaa",
        "Agyapong",
        "Agyapong",
        "Agyare",
        "Agyei",
        "Agyeiwaa",
        "Agyemang",
        "Ahortor",
        "Akoaa",
        "Akoto",
        "Akowua",
        "Akyaa",
        "Akyeamfuɔ",
        "Akyeampomaa",
        "Akyeampong",
        "Akyena",
        "Akyerε",
        "Akyerεko",
        "Akɔmaa",
        "Amo",
        "Amoa",
        "Amoako",
        "Amoakowaa",
        "Amoanimaa",
        "Amoasi",
        "Ampadu",
        "Ampofo",
        "Ampofowaa",
        "Ampoma",
        "Amponsa",
        "Amponsa",
        "Andorful",
        "Anima",
        "Ankra",
        "Anokye",
        "Ansa",
        "Ansomaa",
        "Ansomah",
        "Antwi",
        "Antwi",
        "Antwiwaa",
        "Appia",
        "Appiah",
        "Asamoa",
        "Asamoah",
        "Asante",
        "Asantewaa",
        "Asare",
        "Asenso",
        "Asiama",
        "Asiedu",
        "Asieduwaa",
        "Ata",
        "Ataa",
        "Awuah",
        "Baa",
        "Baafi",
        "Baah",
        "Baawia",
        "Badu",
        "Boadi",
        "Boadu",
        "Boahen",
        "Boakye",
        "Boakye",
        "Boakyewaa",
        "Boatemaa",
        "Boatemaah",
        "Boaten",
        "Boateng",
        "Bona",
        "Bonsra",
        "Bonsu",
        "Daako",
        "Daakoaa",
        "Danso",
        "Darko",
        "Donkor",
        "Duah",
        "Dufie",
        "Dwamena",
        "Fofie",
        "Foriwaa",
        "Fosu",
        "Fosua",
        "Frema",
        "Frimpomaa",
        "Gyamfi",
        "Gyamfi",
        "Gyamfiaa",
        "Gyasi",
        "Gyasiwaa",
        "Karikari",
        "Koomson",
        "Kumi",
        "Kusi",
        "Kusiwaa",
        "Kwaakye",
        "Kwaakyewaa",
        "Kwartemaa",
        "Kwarteng",
        "Kyei",
        "Kyeiwaa",
        "Kyerewaa",
        "Mansa",
        "Mensa",
        "Mensah",
        "Nkansa",
        "Nkansah",
        "Nkrumah",
        "Nsia",
        "Nti",
        "Ntiamoa",
        "Ntim",
        "Nyaako",
        "Nyaakoaa",
        "Nyame",
        "Nyantakyi",
        "Obeng",
        "Ofori",
        "Ofosu",
        "Okyere",
        "Okyere",
        "Omani",
        "Opoku",
        "Oppong",
        "Opuku",
        "Osei",
        "Oti",
        "Otiwa",
        "Otuo",
        "Owusu",
        "Owusuwaa",
        "Pokuaa",
        "Pomaa",
        "Prempeh",
        "Quartey",
        "Safo",
        "Safo",
        "Safoaa",
        "Sarpong",
        "Serwaa",
        "Takyi",
        "Tawia",
        "Tiwaa",
        "Tutu",
        "Tweneboa",
        "Twumasi",
        "Wiafe",
        "Yaamoa",
        "Yawson",
        "Yeboa",
        "Yeboah",
        "Yirenkyi",
    )

    last_names = last_names_male + last_names_female

    prefixes_female = (
        "Mrs.",
        "Ms.",
        "Miss",
        "Dr.",
        "Mama",
        "Maame",
        "Awura",
        "Sista",
        "Osofo Maame",
    )

    prefixes_male = ("Mr.", "Dr.", "Agya", "Owura", "Osofo")
