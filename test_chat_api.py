#!/usr/bin/env python3
"""
Script de test pour l'API de chat
Teste l'envoi de messages et la réception de réponses
"""

import requests
import json
import time

def test_chat_api():
    """Teste l'API de chat"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Test de l'API de chat")
    print("=" * 40)
    
    # 1. Tester l'accès à la page de chat
    print("1. Test d'accès à la page de chat...")
    try:
        response = requests.get(f"{base_url}/support/chat")
        if response.status_code == 200:
            print("   ✅ Page de chat accessible")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")
        return False
    
    # 2. Tester l'API de debug (si admin)
    print("\n2. Test de l'API de debug...")
    try:
        response = requests.get(f"{base_url}/support/api/debug-chat")
        if response.status_code == 200:
            debug_data = response.json()
            print("   ✅ API de debug accessible")
            print(f"   📊 Clé API configurée: {debug_data['environment']['GEMINI_API_KEY_SET']}")
            print(f"   📊 Service disponible: {debug_data['service_status']['gemini_available']}")
            
            if debug_data['connection_test']['success']:
                print("   ✅ Test de connexion Gemini réussi")
            else:
                print(f"   ❌ Test de connexion échoué: {debug_data['connection_test']['error']}")
                
        elif response.status_code == 403:
            print("   ⚠️  Accès refusé (connexion admin requise)")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # 3. Tester l'envoi d'un message (nécessite une session)
    print("\n3. Test d'envoi de message...")
    print("   ⚠️  Nécessite une connexion utilisateur")
    print("   💡 Testez manuellement dans l'interface web")
    
    return True

def test_templates():
    """Teste l'accès aux différentes pages"""
    
    base_url = "http://127.0.0.1:5000"
    pages = [
        ("/support", "Page d'accueil support"),
        ("/support/knowledge-base", "Base de connaissances"),
        ("/support/tickets", "Liste des tickets"),
        ("/support/tickets/create", "Création de ticket"),
    ]
    
    print("\n🌐 Test des templates")
    print("=" * 40)
    
    for url, description in pages:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                print(f"   ✅ {description}")
            elif response.status_code == 302:
                print(f"   🔄 {description} (redirection - connexion requise)")
            else:
                print(f"   ❌ {description} - Erreur {response.status_code}")
        except Exception as e:
            print(f"   ❌ {description} - Erreur: {e}")

def main():
    """Fonction principale"""
    print("🚀 Test complet du système de support AI")
    print("=" * 50)
    
    # Test de l'API
    api_success = test_chat_api()
    
    # Test des templates
    test_templates()
    
    print("\n" + "=" * 50)
    if api_success:
        print("✅ Tests terminés avec succès !")
        print("\n💡 Prochaines étapes :")
        print("   1. Configurez votre clé API Gemini dans .env")
        print("   2. Connectez-vous à l'interface web")
        print("   3. Testez le chat en envoyant des messages")
        print("   4. Vérifiez les réponses de l'IA")
    else:
        print("❌ Certains tests ont échoué")
        print("\n🔧 Vérifiez :")
        print("   1. L'application Flask est démarrée")
        print("   2. Le port 5000 est accessible")
        print("   3. Les templates sont correctement installés")

if __name__ == "__main__":
    main()
