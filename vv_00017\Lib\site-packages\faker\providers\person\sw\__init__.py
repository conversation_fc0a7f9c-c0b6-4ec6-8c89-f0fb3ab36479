from .. import Provider as <PERSON><PERSON><PERSON><PERSON>


class Provider(<PERSON><PERSON><PERSON><PERSON>):
    """
    A Faker provider for generating fake Swahili.
    """

    formats = (
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}}",
        "{{first_name_male}} {{last_name_male}} {{last_name_male}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}}",
        "{{first_name_female}} {{last_name_female}} {{last_name_female}}",
        "{{prefix_male}} {{first_name_male}} {{last_name_male}}",
        "{{prefix_female}} {{first_name_female}} {{last_name_female}}",
        "{{prefix_male}} {{first_name_male}} {{last_name_male}}",
        "{{prefix_female}} {{first_name_female}} {{last_name_female}}",
    )

    # first names sourced from:
    # 1. https://www.behindthename.com/submit/names/gender/masculine/usage/swahili
    # 2. https://github.com/faker-js/faker/blob/next/src/locales/yo_NG/person/male_first_name.ts

    first_names_male = (
        "Abdu",
        "Aijuka",
        "Amri",
        "Andwele",
        "Angalia",
        "Angavu",
        "Anoni",
        "Asani",
        "Asanti",
        "Athumani",
        "Azizi",
        "Bahari",
        "Bale",
        "Balinda",
        "Beshte",
        "Bibuwa",
        "Boma",
        "Cheusi",
        "Chuki",
        "Dai",
        "Daudi",
        "Duma",
        "Dunia",
        "Ëakumbu",
        "Ekundu",
        "Eliakimu",
        "Enzi",
        "Evance",
        "Fahari",
        "Fanaka",
        "Faraja",
        "Hadithi",
        "Hamis",
        "Harambee",
        "Hekima",
        "Isaya",
        "Issack",
        "Ituri",
        "Jalia",
        "Jangwa",
        "Jelani",
        "Jua",
        "Jumaane",
        "Justiniani",
        "Kaombwe",
        "Kashangaki",
        "Kenyangi",
        "Khamani",
        "Khamisi",
        "Kiapo",
        "Kiburi",
        "Kijana",
        "Kijani",
        "Kimbilio",
        "Kinubi",
        "Kipenzi",
        "Kiume",
        "Kondo",
        "Konradi",
        "Kovu",
        "Kurunzi",
        "Kusiima",
        "Makini",
        "Makunga",
        "Makuu",
        "Matunda",
        "Mavuno",
        "Mohamedi",
        "Mulele",
        "Mwezi",
        "Ngamia",
        "Ngeni",
        "Ntimi",
        "Nuhu",
        "Nuriat",
        "Nwabudike",
        "Osogo",
        "Pambe",
        "Pelaji",
        "Popobawa",
        "Pumbaa",
        "Rashidi",
        "Reshoni",
        "Risasi",
        "Rua",
        "Rubani",
        "Ruhiu",
        "Rungo",
        "Sabari",
        "Sadaka",
        "Sadiki",
        "Safari",
        "Samweli",
        "Seif",
        "Shida",
        "Sifa",
        "Siku",
        "Takatifu",
        "Thabiti",
        "Tisa",
        "Tufani",
        "Tukufu",
        "Ushindi",
        "Usiku",
        "Uzima",
        "Wamwema",
        "Yakobo",
        "Yohana",
        "Yohane",
        "Zahur",
        "Zende",
        "Zuba",
        "Zuhri",
        "Zwatie",
    )
    first_names_female = (
        "Abigaili",
        "Adhra",
        "Adia",
        "Adimu",
        "Akumu",
        "Almasi",
        "Amani",
        "Amondi",
        "Anasa",
        "Angalia",
        "Arusi",
        "Asali",
        "Asanti",
        "Asatira",
        "Asmini",
        "Atiena",
        "Bahari",
        "Boma",
        "Busara",
        "Chaniya",
        "Chausiki",
        "Chipukizi",
        "Chuki",
        "Dainess",
        "Dalili",
        "Enzi",
        "Evance",
        "Fahari",
        "Faisa",
        "Fanaka",
        "Faraja",
        "Farhiya",
        "Farijika",
        "Gethera",
        "Goma",
        "Haiba",
        "Halisi",
        "Hanja",
        "Hashiki",
        "Hatima",
        "Hawehindi",
        "Hekima",
        "Hidaya",
        "Hodari",
        "Humaiya",
        "Imany",
        "Imara",
        "Itanya",
        "Jahi",
        "Jana",
        "Jasiri",
        "Jina",
        "Jua",
        "Kaluwa",
        "Kaombwe",
        "Karama",
        "Kaskazi",
        "Kiah",
        "Kibafupia",
        "Kibibi",
        "Kiburi",
        "Kijana",
        "Kimya",
        "Kinaya",
        "Kiojah",
        "Kipenzi",
        "Kipepeo",
        "Kisima",
        "Kiwara",
        "Kuchanua",
        "Kweli",
        "Lailati",
        "Laini",
        "Madaha",
        "Madini",
        "Madoa",
        "Mahali",
        "Maisha",
        "Majani",
        "Makini",
        "Maliza",
        "Marini",
        "Marjani",
        "Matunda",
        "Maua",
        "Misuli",
        "Mkarkara",
        "Mrihani",
        "Muhima",
        "Musila",
        "Mwamini",
        "Mwasaa",
        "Najuma",
        "Naki",
        "Nashipie",
        "Nasra",
        "Nathari",
        "Nayfa",
        "Nelah",
        "Niara",
        "Nigesa",
        "Njozi",
        "Nula",
        "Nyasi",
        "Nyoka",
        "Nyoni",
        "Nyota",
        "Nyuki",
        "Opwonya",
        "Panya",
        "Paskalia",
        "Reshoni",
        "Rua",
        "Sabari",
        "Sadao",
        "Safari",
        "Safiri",
        "Sarabi",
        "Sarafina",
        "Sauti",
        "Serafina",
        "Shani",
        "Shawana",
        "Shida",
        "Sifa",
        "Siku",
        "Skolastika",
        "Sungara",
        "Swala",
        "Tambika",
        "Tamu",
        "Ta-tanisha",
        "Tisa",
        "Tuere",
        "Tufani",
        "Udeera",
        "Ujamaa",
        "Umande",
        "Umoja",
        "Uzima",
        "Waceera",
        "Wamwema",
        "Waridi",
        "Waseme",
        "Yasinta",
        "Zahnya",
        "Zaituni",
        "Zumaridi",
        "Zuwena",
    )

    first_names = first_names_male + first_names_female

    # last names sourced from :
    # 1.https://www.familyeducation.com/baby-names/surname/origin/kenyan
    last_names_male = (
        "Abwao",
        "Adamu",
        "Baharia",
        "Dhadho",
        "Fuli",
        "Hassani",
        "Juma",
        "Kahinu",
        "Kimachu",
        "Kitumaini",
        "Madhubuti",
        "Magombo",
        "Mathenge",
        "Msuya",
        "Naomi",
        "Nazari",
        "Rikke",
        "Sayyid",
        "Simba",
        "Sinema",
        "Wario",
        "Yudas",
        "Abdi",
        "Ali",
        "Akinyi",
        "Anyango",
        "Juma",
        "Kamau",
        "Kibet",
        "Kimani",
        "Maina",
        "Mwangi",
        "Obama",
        "Ochieng",
        "Onyango",
        "Otieno",
        "Mohamed",
        "Hassan",
        "Wafula",
        "Wanjala",
        "Atieno",
        "Kariuki",
        "Kimutai",
        "Kipkorir",
        "Kipkirui",
        "Kipkemei",
        "Kiplagat",
        "Kiprono",
        "Kipsang",
        "Kiptoo",
        "Kipruto",
        "Mumbi",
        "Muthoni",
        "Njeri",
        "Njoroge",
        "Odhiambo",
        "Omondi",
        "Owuor",
        "Wanijiku",
        "Wambui",
        "Abdullahi",
        "Adan",
        "Ahmed",
        "Auma",
        "Barasa",
        "Hussein",
        "Ibrahim",
        "John",
        "Mutai",
        "Omar",
        "Ouma",
        "Waweru",
    )

    # last names are not sex dependant
    last_names_female = last_names_male
    last_names = last_names_male + last_names_female

    prefixes_female = (
        "Mrs.",
        "Ms.",
        "Dr.",
        "Bi.",
        "Mama",
        "Bibi",
        "Madam",
        "Chief",
        "Dkt.",
        "Mheshimiwa",
        "Mwalimu",
        "Mtukufu",
        "Malkia",
        "Mwanamke",
    )

    prefixes_male = (
        "Mr.",
        "Dr.",
        "Bwana",
        "Mzee",
        "Bw.",
        "Dkt.",
        "Mheshimiwa",
        "Mwalimu",
        "Mtukufu",
        "Mfalme",
    )
